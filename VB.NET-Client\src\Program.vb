Imports System
Imports System.Windows.Forms

Namespace LicenseActivation
    ''' <summary>
    ''' The main entry point for the application
    ''' </summary>
    Module Program
        ''' <summary>
        ''' The main entry point for the application
        ''' </summary>
        <STAThread()>
        Sub Main()
            ' Enable visual styles
            Application.EnableVisualStyles()
            Application.SetCompatibleTextRenderingDefault(False)

            ' Set up global exception handling
            AddHandler Application.ThreadException, AddressOf Application_ThreadException
            AddHandler AppDomain.CurrentDomain.UnhandledException, AddressOf CurrentDomain_UnhandledException

            ' Start the application with the main form
            Application.Run(New LicenseActivation.Forms.MainForm())
        End Sub

        ''' <summary>
        ''' Handles unhandled thread exceptions
        ''' </summary>
        Private Sub Application_ThreadException(sender As Object, e As Threading.ThreadExceptionEventArgs)
            ' Log the exception
            LogException(e.Exception)

            ' Show error message
            MessageBox.Show("An unexpected error occurred: " & e.Exception.Message & vbCrLf & vbCrLf &
                           "Please contact support with the following information:" & vbCrLf &
                           "Error: " & e.Exception.GetType().Name & vbCrLf &
                           "Details: " & e.Exception.Message,
                           "Application Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Sub

        ''' <summary>
        ''' Handles unhandled application domain exceptions
        ''' </summary>
        Private Sub CurrentDomain_UnhandledException(sender As Object, e As UnhandledExceptionEventArgs)
            ' Get the exception
            Dim ex As Exception = TryCast(e.ExceptionObject, Exception)

            ' Log the exception
            If ex IsNot Nothing Then
                LogException(ex)

                ' Show error message
                MessageBox.Show("A fatal error occurred: " & ex.Message & vbCrLf & vbCrLf &
                               "The application will now close. Please contact support with the following information:" & vbCrLf &
                               "Error: " & ex.GetType().Name & vbCrLf &
                               "Details: " & ex.Message,
                               "Fatal Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

            ' Exit the application
            Environment.Exit(1)
        End Sub

        ''' <summary>
        ''' Logs an exception to a file
        ''' </summary>
        Private Sub LogException(ex As Exception)
            Try
                ' Create log directory if it doesn't exist
                Dim logDir As String = IO.Path.Combine(Application.StartupPath, "Logs")
                If Not IO.Directory.Exists(logDir) Then
                    IO.Directory.CreateDirectory(logDir)
                End If

                ' Create log file name with date
                Dim logFileName As String = IO.Path.Combine(logDir, "Error_" & DateTime.Now.ToString("yyyyMMdd") & ".log")

                ' Write exception details to log file
                Using writer As New IO.StreamWriter(logFileName, True)
                    writer.WriteLine("===========================================================")
                    writer.WriteLine($"Date/Time: {DateTime.Now}")
                    writer.WriteLine($"Exception Type: {ex.GetType().FullName}")
                    writer.WriteLine($"Message: {ex.Message}")
                    writer.WriteLine($"Stack Trace: {ex.StackTrace}")

                    ' Log inner exception if present
                    Dim innerEx As Exception = ex.InnerException
                    While innerEx IsNot Nothing
                        writer.WriteLine("-----------------------------------------------------------")
                        writer.WriteLine($"Inner Exception Type: {innerEx.GetType().FullName}")
                        writer.WriteLine($"Inner Exception Message: {innerEx.Message}")
                        writer.WriteLine($"Inner Exception Stack Trace: {innerEx.StackTrace}")
                        innerEx = innerEx.InnerException
                    End While

                    writer.WriteLine("===========================================================")
                    writer.WriteLine()
                End Using
            Catch
                ' Ignore errors in the error logger
            End Try
        End Sub
    End Module
End Namespace