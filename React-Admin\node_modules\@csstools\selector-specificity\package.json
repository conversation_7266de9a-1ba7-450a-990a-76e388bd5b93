{"name": "@csstools/selector-specificity", "description": "Determine selector specificity with postcss-selector-parser", "version": "2.2.0", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss-selector-parser": "^6.0.10"}, "devDependencies": {"postcss-selector-parser": "^6.0.10"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "stryker": "stryker run --logLevel error", "test": "node ./test/index.mjs && node ./test/_import.mjs && node ./test/_require.cjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/selector-specificity#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "packages/selector-specificity"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "postcss-selector-parser", "specificity"], "volta": {"extends": "../../package.json"}}