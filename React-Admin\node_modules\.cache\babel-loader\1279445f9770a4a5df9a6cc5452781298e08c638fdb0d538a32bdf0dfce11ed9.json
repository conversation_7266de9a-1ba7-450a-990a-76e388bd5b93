{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nimport { hasXMLHttpRequest } from './utils.js';\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nif (!fetchApi && !XmlHttpRequestApi && !ActiveXObjectApi) {\n  try {\n    import('cross-fetch').then(function (mod) {\n      fetchApi = mod.default;\n    }).catch(function () {});\n  } catch (e) {}\n}\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x = XmlHttpRequestApi ? new XmlHttpRequestApi() : new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\nexport default request;", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "_typeof", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "iterator", "constructor", "prototype", "hasXMLHttpRequest", "fetchApi", "fetch", "undefined", "global", "window", "XmlHttpRequestApi", "XMLHttpRequest", "ActiveXObjectApi", "ActiveXObject", "then", "mod", "default", "catch", "addQueryString", "url", "params", "queryString", "paramName", "encodeURIComponent", "indexOf", "slice", "fetchIt", "fetchOptions", "callback", "altFetch", "resolver", "response", "ok", "statusText", "status", "text", "data", "altResponse", "Promise", "omitFetchOptions", "requestWithFetch", "options", "payload", "queryStringParams", "headers", "customHeaders", "process", "versions", "node", "concat", "version", "platform", "arch", "reqOptions", "requestOptions", "method", "body", "stringify", "<PERSON><PERSON><PERSON>ch", "message", "opt", "err", "requestWithXmlHttpRequest", "x", "open", "crossDomain", "setRequestHeader", "withCredentials", "overrideMimeType", "h", "onreadystatechange", "readyState", "responseText", "send", "console", "log", "request", "Error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/i18next-http-backend/esm/request.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport { hasXMLHttpRequest } from './utils.js';\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nif (!fetchApi && !XmlHttpRequestApi && !ActiveXObjectApi) {\n  try {\n    import('cross-fetch').then(function (mod) {\n      fetchApi = mod.default;\n    }).catch(function () {});\n  } catch (e) {}\n}\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x = XmlHttpRequestApi ? new XmlHttpRequestApi() : new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\nexport default request;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIwB,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIyB,OAAO,CAACxB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK5B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC6B,IAAI,CAAC3B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIyB,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIM,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK7B,CAAC,GAAG8B,MAAM,GAAGC,MAAM,EAAE9B,CAAC,CAAC;AAAE;AAC3T,SAASwB,OAAOA,CAACpB,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOoB,OAAO,GAAG,UAAU,IAAI,OAAOC,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACM,QAAQ,GAAG,UAAU3B,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOqB,MAAM,IAAIrB,CAAC,CAAC4B,WAAW,KAAKP,MAAM,IAAIrB,CAAC,KAAKqB,MAAM,CAACQ,SAAS,GAAG,QAAQ,GAAG,OAAO7B,CAAC;EAAE,CAAC,EAAEoB,OAAO,CAACpB,CAAC,CAAC;AAAE;AAC7T,SAAS8B,iBAAiB,QAAQ,YAAY;AAC9C,IAAIC,QAAQ,GAAG,OAAOC,KAAK,KAAK,UAAU,GAAGA,KAAK,GAAGC,SAAS;AAC9D,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACF,KAAK,EAAE;EACjDD,QAAQ,GAAGG,MAAM,CAACF,KAAK;AACzB,CAAC,MAAM,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACH,KAAK,EAAE;EACxDD,QAAQ,GAAGI,MAAM,CAACH,KAAK;AACzB;AACA,IAAII,iBAAiB;AACrB,IAAIN,iBAAiB,CAAC,CAAC,EAAE;EACvB,IAAI,OAAOI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACG,cAAc,EAAE;IAC1DD,iBAAiB,GAAGF,MAAM,CAACG,cAAc;EAC3C,CAAC,MAAM,IAAI,OAAOF,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACE,cAAc,EAAE;IACjED,iBAAiB,GAAGD,MAAM,CAACE,cAAc;EAC3C;AACF;AACA,IAAIC,gBAAgB;AACpB,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;EACvC,IAAI,OAAOL,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACK,aAAa,EAAE;IACzDD,gBAAgB,GAAGJ,MAAM,CAACK,aAAa;EACzC,CAAC,MAAM,IAAI,OAAOJ,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACI,aAAa,EAAE;IAChED,gBAAgB,GAAGH,MAAM,CAACI,aAAa;EACzC;AACF;AACA,IAAI,OAAOR,QAAQ,KAAK,UAAU,EAAEA,QAAQ,GAAGE,SAAS;AACxD,IAAI,CAACF,QAAQ,IAAI,CAACK,iBAAiB,IAAI,CAACE,gBAAgB,EAAE;EACxD,IAAI;IACF,MAAM,CAAC,aAAa,CAAC,CAACE,IAAI,CAAC,UAAUC,GAAG,EAAE;MACxCV,QAAQ,GAAGU,GAAG,CAACC,OAAO;IACxB,CAAC,CAAC,CAACC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,OAAOjD,CAAC,EAAE,CAAC;AACf;AACA,IAAIkD,cAAc,GAAG,SAASA,cAAcA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACxD,IAAIA,MAAM,IAAI1B,OAAO,CAAC0B,MAAM,CAAC,KAAK,QAAQ,EAAE;IAC1C,IAAIC,WAAW,GAAG,EAAE;IACpB,KAAK,IAAIC,SAAS,IAAIF,MAAM,EAAE;MAC5BC,WAAW,IAAI,GAAG,GAAGE,kBAAkB,CAACD,SAAS,CAAC,GAAG,GAAG,GAAGC,kBAAkB,CAACH,MAAM,CAACE,SAAS,CAAC,CAAC;IAClG;IACA,IAAI,CAACD,WAAW,EAAE,OAAOF,GAAG;IAC5BA,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC;EAC1E;EACA,OAAON,GAAG;AACZ,CAAC;AACD,IAAIO,OAAO,GAAG,SAASA,OAAOA,CAACP,GAAG,EAAEQ,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACpE,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,QAAQ,EAAE;IACzC,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,OAAOJ,QAAQ,CAACG,QAAQ,CAACE,UAAU,IAAI,OAAO,EAAE;MAChEC,MAAM,EAAEH,QAAQ,CAACG;IACnB,CAAC,CAAC;IACFH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACrB,IAAI,CAAC,UAAUsB,IAAI,EAAE;MACnCR,QAAQ,CAAC,IAAI,EAAE;QACbM,MAAM,EAAEH,QAAQ,CAACG,MAAM;QACvBE,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,CAACnB,KAAK,CAACW,QAAQ,CAAC;EACpB,CAAC;EACD,IAAIC,QAAQ,EAAE;IACZ,IAAIQ,WAAW,GAAGR,QAAQ,CAACV,GAAG,EAAEQ,YAAY,CAAC;IAC7C,IAAIU,WAAW,YAAYC,OAAO,EAAE;MAClCD,WAAW,CAACvB,IAAI,CAACgB,QAAQ,CAAC,CAACb,KAAK,CAACW,QAAQ,CAAC;MAC1C;IACF;EACF;EACA,IAAI,OAAOtB,KAAK,KAAK,UAAU,EAAE;IAC/BA,KAAK,CAACa,GAAG,EAAEQ,YAAY,CAAC,CAACb,IAAI,CAACgB,QAAQ,CAAC,CAACb,KAAK,CAACW,QAAQ,CAAC;EACzD,CAAC,MAAM;IACLvB,QAAQ,CAACc,GAAG,EAAEQ,YAAY,CAAC,CAACb,IAAI,CAACgB,QAAQ,CAAC,CAACb,KAAK,CAACW,QAAQ,CAAC;EAC5D;AACF,CAAC;AACD,IAAIW,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,OAAO,EAAEtB,GAAG,EAAEuB,OAAO,EAAEd,QAAQ,EAAE;EAChF,IAAIa,OAAO,CAACE,iBAAiB,EAAE;IAC7BxB,GAAG,GAAGD,cAAc,CAACC,GAAG,EAAEsB,OAAO,CAACE,iBAAiB,CAAC;EACtD;EACA,IAAIC,OAAO,GAAGhE,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO6D,OAAO,CAACI,aAAa,KAAK,UAAU,GAAGJ,OAAO,CAACI,aAAa,CAAC,CAAC,GAAGJ,OAAO,CAACI,aAAa,CAAC;EAC9H,IAAI,OAAOpC,MAAM,KAAK,WAAW,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACsC,OAAO,KAAK,WAAW,IAAItC,MAAM,CAACsC,OAAO,CAACC,QAAQ,IAAIvC,MAAM,CAACsC,OAAO,CAACC,QAAQ,CAACC,IAAI,EAAE;IACtKJ,OAAO,CAAC,YAAY,CAAC,GAAG,6BAA6B,CAACK,MAAM,CAACzC,MAAM,CAACsC,OAAO,CAACI,OAAO,EAAE,IAAI,CAAC,CAACD,MAAM,CAACzC,MAAM,CAACsC,OAAO,CAACK,QAAQ,EAAE,GAAG,CAAC,CAACF,MAAM,CAACzC,MAAM,CAACsC,OAAO,CAACM,IAAI,EAAE,GAAG,CAAC;EAClK;EACA,IAAIV,OAAO,EAAEE,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EACzD,IAAIS,UAAU,GAAG,OAAOZ,OAAO,CAACa,cAAc,KAAK,UAAU,GAAGb,OAAO,CAACa,cAAc,CAACZ,OAAO,CAAC,GAAGD,OAAO,CAACa,cAAc;EACxH,IAAI3B,YAAY,GAAG/C,aAAa,CAAC;IAC/B2E,MAAM,EAAEb,OAAO,GAAG,MAAM,GAAG,KAAK;IAChCc,IAAI,EAAEd,OAAO,GAAGD,OAAO,CAACgB,SAAS,CAACf,OAAO,CAAC,GAAGnC,SAAS;IACtDqC,OAAO,EAAEA;EACX,CAAC,EAAEL,gBAAgB,GAAG,CAAC,CAAC,GAAGc,UAAU,CAAC;EACtC,IAAIxB,QAAQ,GAAG,OAAOY,OAAO,CAACiB,cAAc,KAAK,UAAU,IAAIjB,OAAO,CAACiB,cAAc,CAAC5E,MAAM,IAAI,CAAC,GAAG2D,OAAO,CAACiB,cAAc,GAAGnD,SAAS;EACtI,IAAI;IACFmB,OAAO,CAACP,GAAG,EAAEQ,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;EAChD,CAAC,CAAC,OAAO7D,CAAC,EAAE;IACV,IAAI,CAACqF,UAAU,IAAIlF,MAAM,CAACC,IAAI,CAACiF,UAAU,CAAC,CAACvE,MAAM,KAAK,CAAC,IAAI,CAACd,CAAC,CAAC2F,OAAO,IAAI3F,CAAC,CAAC2F,OAAO,CAACnC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;MACjH,OAAOI,QAAQ,CAAC5D,CAAC,CAAC;IACpB;IACA,IAAI;MACFG,MAAM,CAACC,IAAI,CAACiF,UAAU,CAAC,CAACtE,OAAO,CAAC,UAAU6E,GAAG,EAAE;QAC7C,OAAOjC,YAAY,CAACiC,GAAG,CAAC;MAC1B,CAAC,CAAC;MACFlC,OAAO,CAACP,GAAG,EAAEQ,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;MAC9CU,gBAAgB,GAAG,IAAI;IACzB,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACZjC,QAAQ,CAACiC,GAAG,CAAC;IACf;EACF;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACrB,OAAO,EAAEtB,GAAG,EAAEuB,OAAO,EAAEd,QAAQ,EAAE;EAClG,IAAIc,OAAO,IAAIhD,OAAO,CAACgD,OAAO,CAAC,KAAK,QAAQ,EAAE;IAC5CA,OAAO,GAAGxB,cAAc,CAAC,EAAE,EAAEwB,OAAO,CAAC,CAACjB,KAAK,CAAC,CAAC,CAAC;EAChD;EACA,IAAIgB,OAAO,CAACE,iBAAiB,EAAE;IAC7BxB,GAAG,GAAGD,cAAc,CAACC,GAAG,EAAEsB,OAAO,CAACE,iBAAiB,CAAC;EACtD;EACA,IAAI;IACF,IAAIoB,CAAC,GAAGrD,iBAAiB,GAAG,IAAIA,iBAAiB,CAAC,CAAC,GAAG,IAAIE,gBAAgB,CAAC,oBAAoB,CAAC;IAChGmD,CAAC,CAACC,IAAI,CAACtB,OAAO,GAAG,MAAM,GAAG,KAAK,EAAEvB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACsB,OAAO,CAACwB,WAAW,EAAE;MACxBF,CAAC,CAACG,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;IAC1D;IACAH,CAAC,CAACI,eAAe,GAAG,CAAC,CAAC1B,OAAO,CAAC0B,eAAe;IAC7C,IAAIzB,OAAO,EAAE;MACXqB,CAAC,CAACG,gBAAgB,CAAC,cAAc,EAAE,mCAAmC,CAAC;IACzE;IACA,IAAIH,CAAC,CAACK,gBAAgB,EAAE;MACtBL,CAAC,CAACK,gBAAgB,CAAC,kBAAkB,CAAC;IACxC;IACA,IAAIC,CAAC,GAAG5B,OAAO,CAACI,aAAa;IAC7BwB,CAAC,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,CAAC,GAAGA,CAAC;IACrC,IAAIA,CAAC,EAAE;MACL,KAAK,IAAI7E,CAAC,IAAI6E,CAAC,EAAE;QACfN,CAAC,CAACG,gBAAgB,CAAC1E,CAAC,EAAE6E,CAAC,CAAC7E,CAAC,CAAC,CAAC;MAC7B;IACF;IACAuE,CAAC,CAACO,kBAAkB,GAAG,YAAY;MACjCP,CAAC,CAACQ,UAAU,GAAG,CAAC,IAAI3C,QAAQ,CAACmC,CAAC,CAAC7B,MAAM,IAAI,GAAG,GAAG6B,CAAC,CAAC9B,UAAU,GAAG,IAAI,EAAE;QAClEC,MAAM,EAAE6B,CAAC,CAAC7B,MAAM;QAChBE,IAAI,EAAE2B,CAAC,CAACS;MACV,CAAC,CAAC;IACJ,CAAC;IACDT,CAAC,CAACU,IAAI,CAAC/B,OAAO,CAAC;EACjB,CAAC,CAAC,OAAO1E,CAAC,EAAE;IACV0G,OAAO,IAAIA,OAAO,CAACC,GAAG,CAAC3G,CAAC,CAAC;EAC3B;AACF,CAAC;AACD,IAAI4G,OAAO,GAAG,SAASA,OAAOA,CAACnC,OAAO,EAAEtB,GAAG,EAAEuB,OAAO,EAAEd,QAAQ,EAAE;EAC9D,IAAI,OAAOc,OAAO,KAAK,UAAU,EAAE;IACjCd,QAAQ,GAAGc,OAAO;IAClBA,OAAO,GAAGnC,SAAS;EACrB;EACAqB,QAAQ,GAAGA,QAAQ,IAAI,YAAY,CAAC,CAAC;EACrC,IAAIvB,QAAQ,IAAIc,GAAG,CAACK,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;IAC1C,OAAOgB,gBAAgB,CAACC,OAAO,EAAEtB,GAAG,EAAEuB,OAAO,EAAEd,QAAQ,CAAC;EAC1D;EACA,IAAIxB,iBAAiB,CAAC,CAAC,IAAI,OAAOS,aAAa,KAAK,UAAU,EAAE;IAC9D,OAAOiD,yBAAyB,CAACrB,OAAO,EAAEtB,GAAG,EAAEuB,OAAO,EAAEd,QAAQ,CAAC;EACnE;EACAA,QAAQ,CAAC,IAAIiD,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAClE,CAAC;AACD,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}