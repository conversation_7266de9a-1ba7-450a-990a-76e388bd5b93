/**
 * Supabase configuration for the License Verification API
 * Sets up and provides access to the Supabase client
 */

const { createClient } = require('@supabase/supabase-js');
const { setupLogging } = require('../utils/logger');

// Initialize logger
const logger = setupLogging();

// Supabase client instance
let supabaseClient = null;

/**
 * Setup and initialize the Supabase client
 * @returns {Object} Supabase client instance
 */
const setupSupabase = () => {
  try {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase URL and key must be provided in environment variables');
    }

    supabaseClient = createClient(supabaseUrl, supabaseKey);
    logger.info('Supabase client initialized successfully');
    return supabaseClient;
  } catch (error) {
    logger.error(`Failed to initialize Supabase client: ${error.message}`);
    throw error;
  }
};

/**
 * Get the Supabase client instance
 * @returns {Object} Supabase client instance
 */
const getSupabaseClient = () => {
  if (!supabaseClient) {
    return setupSupabase();
  }
  return supabaseClient;
};

module.exports = {
  setupSupabase,
  getSupabaseClient
};