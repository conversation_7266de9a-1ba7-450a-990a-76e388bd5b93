Imports System
Imports System.Drawing
Imports System.Drawing.Drawing2D
Imports System.Drawing.Imaging
Imports System.IO
Imports System.Windows.Forms

Namespace LicenseActivation.Modules
    ''' <summary>
    ''' Provides utility methods for the application
    ''' </summary>
    Public Module Utilities
        ''' <summary>
        ''' Formats a date to a readable string
        ''' </summary>
        ''' <param name="date">The date to format</param>
        ''' <returns>A formatted date string</returns>
        Public Function FormatDate(date As DateTime) As String
            Return date.ToString("MMMM dd, yyyy")
        End Function

        ''' <summary>
        ''' Gets the color for a license type
        ''' </summary>
        ''' <param name="licenseType">The license type</param>
        ''' <returns>A color corresponding to the license type</returns>
        Public Function GetLicenseTypeColor(licenseType As String) As Color
            Select Case licenseType.ToLower()
                Case "standard"
                    Return Color.FromArgb(59, 130, 246) ' Blue
                Case "premium"
                    Return Color.FromArgb(16, 185, 129) ' Green
                Case "enterprise"
                    Return Color.FromArgb(245, 158, 11) ' Amber
                Case "gold"
                    Return Color.FromArgb(244, 196, 46) ' Gold
                Case "platinum"
                    Return Color.FromArgb(108, 115, 132) ' Platinum
                Case Else
                    Return Color.FromArgb(107, 114, 128) ' Gray
            End Select
        End Function

        ''' <summary>
        ''' Gets the background color for a license type
        ''' </summary>
        ''' <param name="licenseType">The license type</param>
        ''' <returns>A background color corresponding to the license type</returns>
        Public Function GetLicenseTypeBackgroundColor(licenseType As String) As Color
            Select Case licenseType.ToLower()
                Case "standard"
                    Return Color.FromArgb(239, 246, 255) ' Light Blue
                Case "premium"
                    Return Color.FromArgb(236, 253, 245) ' Light Green
                Case "enterprise"
                    Return Color.FromArgb(255, 251, 235) ' Light Amber
                Case "gold"
                    Return Color.FromArgb(253, 248, 227) ' Light Gold
                Case "platinum"
                    Return Color.FromArgb(248, 249, 250) ' Light Platinum
                Case Else
                    Return Color.FromArgb(243, 244, 246) ' Light Gray
            End Select
        End Function

        ''' <summary>
        ''' Creates a rounded rectangle path
        ''' </summary>
        ''' <param name="rect">The rectangle</param>
        ''' <param name="radius">The corner radius</param>
        ''' <returns>A GraphicsPath for the rounded rectangle</returns>
        Public Function CreateRoundedRectangle(rect As Rectangle, radius As Integer) As GraphicsPath
            Dim path As New GraphicsPath()
            Dim diameter As Integer = radius * 2

            ' Top left corner
            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90)
            ' Top right corner
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90)
            ' Bottom right corner
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90)
            ' Bottom left corner
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90)

            ' Close the path
            path.CloseFigure()

            Return path
        End Function

        ''' <summary>
        ''' Resizes an image to the specified dimensions
        ''' </summary>
        ''' <param name="image">The image to resize</param>
        ''' <param name="width">The target width</param>
        ''' <param name="height">The target height</param>
        ''' <returns>The resized image</returns>
        Public Function ResizeImage(image As Image, width As Integer, height As Integer) As Bitmap
            Dim result As New Bitmap(width, height)

            Using g As Graphics = Graphics.FromImage(result)
                g.InterpolationMode = InterpolationMode.HighQualityBicubic
                g.SmoothingMode = SmoothingMode.HighQuality
                g.PixelOffsetMode = PixelOffsetMode.HighQuality
                g.CompositingQuality = CompositingQuality.HighQuality
                g.DrawImage(image, 0, 0, width, height)
            End Using

            Return result
        End Function

        ''' <summary>
        ''' Saves an image to a file with the specified format
        ''' </summary>
        ''' <param name="image">The image to save</param>
        ''' <param name="filePath">The file path</param>
        ''' <param name="format">The image format</param>
        ''' <returns>True if the image was saved successfully, otherwise false</returns>
        Public Function SaveImage(image As Image, filePath As String, format As ImageFormat) As Boolean
            Try
                image.Save(filePath, format)
                Return True
            Catch ex As Exception
                MessageBox.Show($"Error saving image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return False
            End Try
        End Function

        ''' <summary>
        ''' Applies a gold gradient to an image
        ''' </summary>
        ''' <param name="image">The image to apply the gradient to</param>
        ''' <returns>The image with a gold gradient</returns>
        Public Function ApplyGoldGradient(image As Bitmap) As Bitmap
            Dim result As New Bitmap(image.Width, image.Height)

            Using g As Graphics = Graphics.FromImage(result)
                ' Create a gold gradient brush
                Using brush As New LinearGradientBrush(
                    New Point(0, 0),
                    New Point(image.Width, image.Height),
                    Color.FromArgb(255, 215, 0), ' Gold
                    Color.FromArgb(184, 134, 11)) ' Dark gold

                    ' Draw the original image
                    g.DrawImage(image, 0, 0, image.Width, image.Height)

                    ' Apply the gold gradient with transparency
                    Dim colorMatrix As New ColorMatrix()
                    colorMatrix.Matrix33 = 0.3F ' Opacity

                    Using attributes As New ImageAttributes()
                        attributes.SetColorMatrix(colorMatrix)

                        ' Fill the rectangle with the gold gradient
                        g.FillRectangle(brush, New Rectangle(0, 0, image.Width, image.Height))
                    End Using
                End Using
            End Using

            Return result
        End Function

        ''' <summary>
        ''' Applies a platinum gradient to an image
        ''' </summary>
        ''' <param name="image">The image to apply the gradient to</param>
        ''' <returns>The image with a platinum gradient</returns>
        Public Function ApplyPlatinumGradient(image As Bitmap) As Bitmap
            Dim result As New Bitmap(image.Width, image.Height)

            Using g As Graphics = Graphics.FromImage(result)
                ' Create a platinum gradient brush
                Using brush As New LinearGradientBrush(
                    New Point(0, 0),
                    New Point(image.Width, image.Height),
                    Color.FromArgb(229, 228, 226), ' Light platinum
                    Color.FromArgb(108, 115, 132)) ' Dark platinum

                    ' Draw the original image
                    g.DrawImage(image, 0, 0, image.Width, image.Height)

                    ' Apply the platinum gradient with transparency
                    Dim colorMatrix As New ColorMatrix()
                    colorMatrix.Matrix33 = 0.3F ' Opacity

                    Using attributes As New ImageAttributes()
                        attributes.SetColorMatrix(colorMatrix)

                        ' Fill the rectangle with the platinum gradient
                        g.FillRectangle(brush, New Rectangle(0, 0, image.Width, image.Height))
                    End Using
                End Using
            End Using

            Return result
        End Function
    End Module
End Namespace