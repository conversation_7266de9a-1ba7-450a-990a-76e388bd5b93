{"compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": true, "charset": "utf8", "checkJs": false, "declaration": true, "disableSizeLimit": false, "downlevelIteration": false, "emitBOM": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "importHelpers": false, "inlineSourceMap": false, "inlineSources": false, "isolatedModules": false, "lib": ["es2017", "esnext.asynciterable"], "locale": "en-us", "module": "commonjs", "moduleResolution": "node", "newLine": "lf", "noEmit": false, "noEmitHelpers": false, "noEmitOnError": true, "noErrorTruncation": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noStrictGenericChecks": false, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitUseStrict": false, "noLib": false, "noResolve": false, "preserveConstEnums": true, "removeComments": false, "skipLibCheck": true, "sourceMap": true, "strict": true, "strictNullChecks": true, "suppressExcessPropertyErrors": false, "suppressImplicitAnyIndexErrors": false, "target": "es2017", "traceResolution": false, "rootDir": "", "outDir": "../../build/lib", "typeRoots": []}, "include": ["**/*.ts"], "exclude": []}