!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("void-elements")):"function"==typeof define&&define.amd?define(["void-elements"],t):(e=e||self).htmlParseStringify=t(e.voidElements)}(this,function(e){e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e;var t=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function n(n){var r={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},i=n.match(/<\/?([^\s]+?)[/\s>]/);if(i&&(r.name=i[1],(e[i[1]]||"/"===n.charAt(n.length-2))&&(r.voidElement=!0),r.name.startsWith("!--"))){var s=n.indexOf("--\x3e");return{type:"comment",comment:-1!==s?n.slice(4,s):""}}for(var c=new RegExp(t),o=null;null!==(o=c.exec(n));)if(o[0].trim())if(o[1]){var a=o[1].trim(),l=[a,""];a.indexOf("=")>-1&&(l=a.split("=")),r.attrs[l[0]]=l[1],c.lastIndex--}else o[2]&&(r.attrs[o[2]]=o[3].trim().substring(1,o[3].length-1));return r}var r=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,i=/^\s*$/,s=Object.create(null);function c(e,t){switch(t.type){case"text":return e+t.content;case"tag":return e+="<"+t.name+(t.attrs?function(e){var t=[];for(var n in e)t.push(n+'="'+e[n]+'"');return t.length?" "+t.join(" "):""}(t.attrs):"")+(t.voidElement?"/>":">"),t.voidElement?e:e+t.children.reduce(c,"")+"</"+t.name+">";case"comment":return e+"\x3c!--"+t.comment+"--\x3e"}}return{parse:function(e,t){t||(t={}),t.components||(t.components=s);var c,o=[],a=[],l=-1,u=!1;if(0!==e.indexOf("<")){var f=e.indexOf("<");o.push({type:"text",content:-1===f?e:e.substring(0,f)})}return e.replace(r,function(r,s){if(u){if(r!=="</"+c.name+">")return;u=!1}var f,m="/"!==r.charAt(1),d=r.startsWith("\x3c!--"),p=s+r.length,h=e.charAt(p);if(d){var v=n(r);return l<0?(o.push(v),o):((f=a[l]).children.push(v),o)}if(m&&(l++,"tag"===(c=n(r)).type&&t.components[c.name]&&(c.type="component",u=!0),c.voidElement||u||!h||"<"===h||c.children.push({type:"text",content:e.slice(p,e.indexOf("<",p))}),0===l&&o.push(c),(f=a[l-1])&&f.children.push(c),a[l]=c),(!m||c.voidElement)&&(l>-1&&(c.voidElement||c.name===r.slice(2,-1))&&(l--,c=-1===l?o:a[l]),!u&&"<"!==h&&h)){f=-1===l?o:a[l].children;var x=e.indexOf("<",p),g=e.slice(p,-1===x?void 0:x);i.test(g)&&(g=" "),(x>-1&&l+f.length>=0||" "!==g)&&f.push({type:"text",content:g})}}),o},stringify:function(e){return e.reduce(function(e,t){return e+c("",t)},"")}}});
//# sourceMappingURL=html-parse-stringify.umd.js.map
