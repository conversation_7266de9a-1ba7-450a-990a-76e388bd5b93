"use strict";var e=require("postcss-selector-parser"),o=require("@csstools/selector-specificity");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=s(e);function t(e,o){return n.default.isPseudoElement(e)?d.pseudoElement:d[o]}const d={universal:0,tag:1,pseudoElement:2,id:3,class:4,attribute:5,pseudo:6,selector:7,string:8,root:9,comment:10};function r(e,o,s,d){return e.flatMap((e=>{if(-1===e.indexOf(":-csstools-matches")&&-1===e.toLowerCase().indexOf(":is"))return e;const r=n.default().astSync(e);return r.walkPseudos((e=>{if(":is"===e.value.toLowerCase()&&e.nodes&&e.nodes.length&&"selector"===e.nodes[0].type&&0===e.nodes[0].nodes.length)return e.value=":not",void e.nodes[0].append(n.default.universal());if(":-csstools-matches"===e.value)if(!e.nodes||e.nodes.length){if(e.walkPseudos((e=>{if(n.default.isPseudoElement(e)){let o=e.value;if(o.startsWith("::-csstools-invalid-"))return;for(;o.startsWith(":");)o=o.slice(1);e.value=`::-csstools-invalid-${o}`,d()}})),1===e.nodes.length&&"selector"===e.nodes[0].type){if(1===e.nodes[0].nodes.length)return void e.replaceWith(e.nodes[0].nodes[0]);if(!e.nodes[0].some((e=>"combinator"===e.type)))return void e.replaceWith(...e.nodes[0].nodes)}1!==r.nodes.length||"selector"!==r.nodes[0].type||1!==r.nodes[0].nodes.length||r.nodes[0].nodes[0]!==e?function(e){return!(!e||!e.nodes||"selector"!==e.type||3!==e.nodes.length||!e.nodes[0]||"pseudo"!==e.nodes[0].type||":-csstools-matches"!==e.nodes[0].value||!e.nodes[1]||"combinator"!==e.nodes[1].type||"+"!==e.nodes[1].value||!e.nodes[2]||"pseudo"!==e.nodes[2].type||":-csstools-matches"!==e.nodes[2].value||!e.nodes[0].nodes||1!==e.nodes[0].nodes.length||"selector"!==e.nodes[0].nodes[0].type||!e.nodes[0].nodes[0].nodes||3!==e.nodes[0].nodes[0].nodes.length||!e.nodes[0].nodes[0].nodes||"combinator"!==e.nodes[0].nodes[0].nodes[1].type||">"!==e.nodes[0].nodes[0].nodes[1].value||!e.nodes[2].nodes||1!==e.nodes[2].nodes.length||"selector"!==e.nodes[2].nodes[0].type||!e.nodes[2].nodes[0].nodes||3!==e.nodes[2].nodes[0].nodes.length||!e.nodes[2].nodes[0].nodes||"combinator"!==e.nodes[2].nodes[0].nodes[1].type||">"!==e.nodes[2].nodes[0].nodes[1].value||(e.nodes[0].nodes[0].insertAfter(e.nodes[0].nodes[0].nodes[0],e.nodes[2].nodes[0].nodes[0].clone()),e.nodes[2].nodes[0].nodes[1].remove(),e.nodes[2].nodes[0].nodes[0].remove(),e.nodes[0].replaceWith(e.nodes[0].nodes[0]),e.nodes[2].replaceWith(e.nodes[2].nodes[0]),0))}(e.parent)||function(e){if(!e||!e.nodes)return!1;if("selector"!==e.type)return!1;if(2!==e.nodes.length)return!1;let o,s;return e.nodes[0]&&"pseudo"===e.nodes[0].type&&":-csstools-matches"===e.nodes[0].value?(o=0,s=1):e.nodes[1]&&"pseudo"===e.nodes[1].type&&":-csstools-matches"===e.nodes[1].value&&(o=1,s=0),!(!o||!e.nodes[s]||"selector"===e.nodes[s].type&&e.nodes[s].some((e=>"combinator"===e.type||n.default.isPseudoElement(e)))||(e.nodes[o].append(e.nodes[s].clone()),e.nodes[o].replaceWith(...e.nodes[o].nodes),e.nodes[s].remove(),0))}(e.parent)||("warning"===o.onComplexSelector&&s(),e.value=":is"):e.replaceWith(...e.nodes[0].nodes)}else e.remove()})),r.walk((e=>{"selector"===e.type&&"nodes"in e&&1===e.nodes.length&&"selector"===e.nodes[0].type&&e.replaceWith(e.nodes[0])})),r.walk((e=>{"nodes"in e&&function(e){if(!e||!e.nodes)return;const o=[];let s=[];for(let t=0;t<e.nodes.length;t++)"combinator"!==e.nodes[t].type?n.default.isPseudoElement(e.nodes[t])?(o.push(s),s=[e.nodes[t]]):s.push(e.nodes[t]):(o.push(s),o.push([e.nodes[t]]),s=[]);o.push(s);const d=[];for(let e=0;e<o.length;e++){const s=o[e];s.sort(((e,o)=>"selector"===e.type&&"selector"===o.type&&e.nodes.length&&o.nodes.length?t(e.nodes[0],e.nodes[0].type)-t(o.nodes[0],o.nodes[0].type):"selector"===e.type&&e.nodes.length?t(e.nodes[0],e.nodes[0].type)-t(o,o.type):"selector"===o.type&&o.nodes.length?t(e,e.type)-t(o.nodes[0],o.nodes[0].type):t(e,e.type)-t(o,o.type)));for(let e=0;e<s.length;e++)d.push(s[e])}for(let o=d.length-1;o>=0;o--)d[o].remove(),e.prepend(d[o])}(e)})),r.toString()})).filter((e=>!!e))}function l(e,s,t=0){const d=":not(#"+s.specificityMatchingName+")",r=":not(."+s.specificityMatchingName+")",c=":not("+s.specificityMatchingName+")";return e.flatMap((e=>{if(-1===e.toLowerCase().indexOf(":is"))return e;let i=!1;const a=[];if(n.default().astSync(e).walkPseudos((e=>{if(":is"!==e.value.toLowerCase()||!e.nodes||!e.nodes.length)return;if("selector"===e.nodes[0].type&&0===e.nodes[0].nodes.length)return;let s=e.parent;for(;s;){if(s.value&&":is"===s.value.toLowerCase()&&"pseudo"===s.type)return void(i=!0);s=s.parent}const n=o.selectorSpecificity(e),t=e.sourceIndex,l=t+e.toString().length,u=[];e.nodes.forEach((e=>{const s={start:t,end:l,option:""},i=o.selectorSpecificity(e);let a=e.toString().trim();const p=Math.max(0,n.a-i.a),f=Math.max(0,n.b-i.b),h=Math.max(0,n.c-i.c);for(let e=0;e<p;e++)a+=d;for(let e=0;e<f;e++)a+=r;for(let e=0;e<h;e++)a+=c;s.option=a,u.push(s)})),a.push(u)})),!a.length)return[e];let u=[];return function(...e){const o=[],s=e.length-1;function n(t,d){for(let r=0,l=e[d].length;r<l;r++){const l=t.slice(0);l.push(e[d][r]),d==s?o.push(l):n(l,d+1)}}return n([],0),o}(...a).forEach((o=>{let s="";for(let t=0;t<o.length;t++){var n;const d=o[t];s+=e.substring((null==(n=o[t-1])?void 0:n.end)||0,o[t].start),s+=":-csstools-matches("+d.option+")",t===o.length-1&&(s+=e.substring(o[t].end))}u.push(s)})),i&&t<10&&(u=l(u,s,t+1)),u})).filter((e=>!!e))}const c=e=>{const o={specificityMatchingName:"does-not-exist",...e||{}};return{postcssPlugin:"postcss-is-pseudo-class",Rule(e,{result:s}){if(!e.selector)return;if(-1===e.selector.toLowerCase().indexOf(":is"))return;let n=!1;const t=()=>{"warning"===o.onComplexSelector&&(n||(n=!0,e.warn(s,`Complex selectors in '${e.selector}' can not be transformed to an equivalent selector without ':is()'.`)))};let d=!1;const c=()=>{"warning"===o.onPseudoElement&&(d||(d=!0,e.warn(s,`Pseudo elements are not allowed in ':is()', unable to transform '${e.selector}'`)))};try{let s=!1;const n=[],d=r(l(e.selectors,{specificityMatchingName:o.specificityMatchingName}),{onComplexSelector:o.onComplexSelector},t,c);if(Array.from(new Set(d)).forEach((o=>{e.selectors.indexOf(o)>-1?n.push(o):(e.cloneBefore({selector:o}),s=!0)})),n.length&&s&&e.cloneBefore({selectors:n}),!o.preserve){if(!s)return;e.remove()}}catch(o){if(o.message.indexOf("call stack size exceeded")>-1)throw o;e.warn(s,`Failed to parse selector "${e.selector}"`)}}}};c.postcss=!0,module.exports=c;
