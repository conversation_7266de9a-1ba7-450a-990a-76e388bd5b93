# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.3](https://github.com/es-shims/Object.groupBy/compare/v1.0.2...v1.0.3) - 2024-03-18

### Commits

- [Deps] update `call-bind`, `es-abstract` [`f1d3e70`](https://github.com/es-shims/Object.groupBy/commit/f1d3e701aff0a36e4d7373059812a9b978c7ad7f)
- [Dev <PERSON><PERSON>] update `tape` [`272a736`](https://github.com/es-shims/Object.groupBy/commit/272a73672f27f90fd6d5054ca13e039c45815a8a)
- [meta] add missing `engines.node` [`7a9c8b0`](https://github.com/es-shims/Object.groupBy/commit/7a9c8b0f636a5703ea923c9d0721fbf5861c6949)

## [v1.0.2](https://github.com/es-shims/Object.groupBy/compare/v1.0.1...v1.0.2) - 2024-02-04

### Commits

- [Refactor] use `es-errors` where possible, so things that only need those do not need `get-intrinsic` [`a6c01d0`](https://github.com/es-shims/Object.groupBy/commit/a6c01d0ec46e7bb5ac68e8bfdce3a64fddc6b0a1)
- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `get-intrinsic` [`65383da`](https://github.com/es-shims/Object.groupBy/commit/65383dad0b036ad3459def995c223a4afb1f6a50)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`e8aeb5b`](https://github.com/es-shims/Object.groupBy/commit/e8aeb5b7b1d88bfbe8be1da369a374ec36cb459f)
- [Robustness] `filter` is not available pre-ES5 [`8f185b8`](https://github.com/es-shims/Object.groupBy/commit/8f185b851f155e41442714bea792b07df778f986)

## [v1.0.1](https://github.com/es-shims/Object.groupBy/compare/v1.0.0...v1.0.1) - 2023-08-28

### Commits

- [Deps] update `es-abstract` [`3ecdf79`](https://github.com/es-shims/Object.groupBy/commit/3ecdf797231a5a8fc4cf1a772ad0892257c11edc)
- [Dev Deps] update `tape` [`793301b`](https://github.com/es-shims/Object.groupBy/commit/793301b6b41750c6682df2c7bff46a4e52ce7a7c)

## v1.0.0 - 2023-07-11

### Commits

- Initial implementation, tests, readme [`77809f3`](https://github.com/es-shims/Object.groupBy/commit/77809f3024955519d71a2ab6ed9883e4d496a953)
- Initial commit [`24ea8cd`](https://github.com/es-shims/Object.groupBy/commit/24ea8cdc625987930d5cf9df0dbff01e5693a544)
- npm init [`36a7d4d`](https://github.com/es-shims/Object.groupBy/commit/36a7d4d9d2fc9ff8503985f2fcf76a2ff4097140)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`bd7b39f`](https://github.com/es-shims/Object.groupBy/commit/bd7b39fca3d5fbff1e7140ae69893ec4694e4201)
- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`9615141`](https://github.com/es-shims/Object.groupBy/commit/9615141fab908eec83e310d8fdf6847a808baf36)
- Only apps should have lockfiles [`63b79a9`](https://github.com/es-shims/Object.groupBy/commit/63b79a97732802eb25da26928646e6ef103762cd)
