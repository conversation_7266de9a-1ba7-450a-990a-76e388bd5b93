import E,{createContext as I,Fragment as H,useContext as x,useEffect as h,useMemo as S,useReducer as G,useRef as R}from"react";import{useEvent as A}from'../../hooks/use-event.js';import{useId as U}from'../../hooks/use-id.js';import{useResolveButtonType as j}from'../../hooks/use-resolve-button-type.js';import{optionalRef as W,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{OpenClosedProvider as $,State as b,useOpenClosed as J}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as X}from'../../utils/bugs.js';import{match as O}from'../../utils/match.js';import{getOwnerDocument as q}from'../../utils/owner.js';import{Features as w,forwardRefWithAs as B,render as k,useMergeRefsFn as N}from'../../utils/render.js';import{startTransition as z}from'../../utils/start-transition.js';import{Keys as g}from'../keyboard.js';var Q=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Q||{}),V=(t=>(t[t.ToggleDisclosure=0]="ToggleDisclosure",t[t.CloseDisclosure=1]="CloseDisclosure",t[t.SetButtonId=2]="SetButtonId",t[t.SetPanelId=3]="SetPanelId",t[t.LinkPanel=4]="LinkPanel",t[t.UnlinkPanel=5]="UnlinkPanel",t))(V||{});let Y={[0]:e=>({...e,disclosureState:O(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[4](e){return e.linkedPanel===!0?e:{...e,linkedPanel:!0}},[5](e){return e.linkedPanel===!1?e:{...e,linkedPanel:!1}},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},M=I(null);M.displayName="DisclosureContext";function _(e){let n=x(M);if(n===null){let o=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,_),o}return n}let v=I(null);v.displayName="DisclosureAPIContext";function K(e){let n=x(v);if(n===null){let o=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,K),o}return n}let F=I(null);F.displayName="DisclosurePanelContext";function Z(){return x(F)}function ee(e,n){return O(n.type,Y,e,n)}let te=H;function ne(e,n){let{defaultOpen:o=!1,...i}=e,f=R(null),l=L(n,W(u=>{f.current=u},e.as===void 0||e.as===H)),t=R(null),d=R(null),s=G(ee,{disclosureState:o?0:1,linkedPanel:!1,buttonRef:d,panelRef:t,buttonId:null,panelId:null}),[{disclosureState:c,buttonId:a},D]=s,p=A(u=>{D({type:1});let y=q(f);if(!y||!a)return;let r=(()=>u?u instanceof HTMLElement?u:u.current instanceof HTMLElement?u.current:y.getElementById(a):y.getElementById(a))();r==null||r.focus()}),P=S(()=>({close:p}),[p]),T=S(()=>({open:c===0,close:p}),[c,p]),C={ref:l};return E.createElement(M.Provider,{value:s},E.createElement(v.Provider,{value:P},E.createElement($,{value:O(c,{[0]:b.Open,[1]:b.Closed})},k({ourProps:C,theirProps:i,slot:T,defaultTag:te,name:"Disclosure"}))))}let le="button";function oe(e,n){let o=U(),{id:i=`headlessui-disclosure-button-${o}`,...f}=e,[l,t]=_("Disclosure.Button"),d=Z(),s=d===null?!1:d===l.panelId,c=R(null),a=L(c,n,s?null:l.buttonRef),D=N();h(()=>{if(!s)return t({type:2,buttonId:i}),()=>{t({type:2,buttonId:null})}},[i,t,s]);let p=A(r=>{var m;if(s){if(l.disclosureState===1)return;switch(r.key){case g.Space:case g.Enter:r.preventDefault(),r.stopPropagation(),t({type:0}),(m=l.buttonRef.current)==null||m.focus();break}}else switch(r.key){case g.Space:case g.Enter:r.preventDefault(),r.stopPropagation(),t({type:0});break}}),P=A(r=>{switch(r.key){case g.Space:r.preventDefault();break}}),T=A(r=>{var m;X(r.currentTarget)||e.disabled||(s?(t({type:0}),(m=l.buttonRef.current)==null||m.focus()):t({type:0}))}),C=S(()=>({open:l.disclosureState===0}),[l]),u=j(e,c),y=s?{ref:a,type:u,onKeyDown:p,onClick:T}:{ref:a,id:i,type:u,"aria-expanded":l.disclosureState===0,"aria-controls":l.linkedPanel?l.panelId:void 0,onKeyDown:p,onKeyUp:P,onClick:T};return k({mergeRefs:D,ourProps:y,theirProps:f,slot:C,defaultTag:le,name:"Disclosure.Button"})}let re="div",se=w.RenderStrategy|w.Static;function ue(e,n){let o=U(),{id:i=`headlessui-disclosure-panel-${o}`,...f}=e,[l,t]=_("Disclosure.Panel"),{close:d}=K("Disclosure.Panel"),s=N(),c=L(n,l.panelRef,T=>{z(()=>t({type:T?4:5}))});h(()=>(t({type:3,panelId:i}),()=>{t({type:3,panelId:null})}),[i,t]);let a=J(),D=(()=>a!==null?(a&b.Open)===b.Open:l.disclosureState===0)(),p=S(()=>({open:l.disclosureState===0,close:d}),[l,d]),P={ref:c,id:i};return E.createElement(F.Provider,{value:l.panelId},k({mergeRefs:s,ourProps:P,theirProps:f,slot:p,defaultTag:re,features:se,visible:D,name:"Disclosure.Panel"}))}let ie=B(ne),ae=B(oe),pe=B(ue),Ae=Object.assign(ie,{Button:ae,Panel:pe});export{Ae as Disclosure};
