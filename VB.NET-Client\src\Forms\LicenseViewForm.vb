Imports System
Imports System.Drawing
Imports System.Windows.Forms

Namespace LicenseActivation.Forms
    ''' <summary>
    ''' Form to display license details
    ''' </summary>
    Public Class LicenseViewForm
        Inherits Form
        Private ReadOnly _license As LicenseActivation.Classes.License
        Private ReadOnly _qrCodeGenerator As LicenseActivation.Classes.QRCodeGenerator

        ''' <summary>
        ''' Initializes a new instance of the LicenseViewForm class
        ''' </summary>
        ''' <param name="license">The license to display</param>
        Public Sub New(license As LicenseActivation.Classes.License)
            ' This call is required by the designer
            InitializeComponent()

            ' Set license
            _license = license
            _qrCodeGenerator = New LicenseActivation.Classes.QRCodeGenerator()

            ' Set form properties
            Me.Text = "License Details"
            ' Me.Icon = My.Resources.AppIcon ' Resource not available

            ' Add event handlers
            AddHandler Me.Load, AddressOf LicenseViewForm_Load
            AddHandler btnClose.Click, AddressOf BtnClose_Click
            AddHandler btnPrint.Click, AddressOf BtnPrint_Click
        End Sub

        ''' <summary>
        ''' Required designer variable
        ''' </summary>
        Private components As System.ComponentModel.IContainer = Nothing

        ''' <summary>
        ''' UI Controls
        ''' </summary>
        Private WithEvents pnlHeader As Panel
        Private WithEvents lblTitle As Label
        Private WithEvents pnlContent As Panel
        Private WithEvents lblLicenseKey As Label
        Private WithEvents txtLicenseKey As TextBox
        Private WithEvents lblLicenseType As Label
        Private WithEvents txtLicenseType As TextBox
        Private WithEvents lblCustomerName As Label
        Private WithEvents txtCustomerName As TextBox
        Private WithEvents lblCustomerEmail As Label
        Private WithEvents txtCustomerEmail As TextBox
        Private WithEvents lblIssueDate As Label
        Private WithEvents txtIssueDate As TextBox
        Private WithEvents lblExpiryDate As Label
        Private WithEvents txtExpiryDate As TextBox
        Private WithEvents lblFeatures As Label
        Private WithEvents lstFeatures As ListBox
        Private WithEvents picQRCode As PictureBox
        Private WithEvents btnPrint As Button
        Private WithEvents btnClose As Button
        Private WithEvents pnlLicenseCard As Panel
        Private WithEvents lblCardTitle As Label
        Private WithEvents lblCardCustomerName As Label
        Private WithEvents lblCardLicenseType As Label
        Private WithEvents lblCardExpiryDate As Label
        Private WithEvents picCardQRCode As PictureBox
        Private WithEvents lblCardLicenseKey As Label



        ''' <summary>
        ''' Required method for Designer support - do not modify
        ''' the contents of this method with the code editor
        ''' </summary>
        Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(800, 600)
            Me.StartPosition = FormStartPosition.CenterParent
            Me.FormBorderStyle = FormBorderStyle.FixedDialog
            Me.MaximizeBox = False
            Me.MinimizeBox = False

            ' Create header panel
            pnlHeader = New Panel()
            pnlHeader.Dock = DockStyle.Top
            pnlHeader.Height = 60
            pnlHeader.BackColor = Color.FromArgb(59, 130, 246) ' Blue

            ' Create title label
            lblTitle = New Label()
            lblTitle.Text = "License Details"
            lblTitle.Font = New Font("Segoe UI", 16, FontStyle.Bold)
            lblTitle.ForeColor = Color.White
            lblTitle.AutoSize = True
            lblTitle.Location = New Point(20, 15)

            ' Create content panel
            pnlContent = New Panel()
            pnlContent.Dock = DockStyle.Fill
            pnlContent.Padding = New Padding(20)

            ' Create license key label
            lblLicenseKey = New Label()
            lblLicenseKey.Text = "License Key:"
            lblLicenseKey.Font = New Font("Segoe UI", 10)
            lblLicenseKey.AutoSize = True
            lblLicenseKey.Location = New Point(20, 20)

            ' Create license key textbox
            txtLicenseKey = New TextBox()
            txtLicenseKey.Width = 350
            txtLicenseKey.Height = 25
            txtLicenseKey.Font = New Font("Segoe UI", 10)
            txtLicenseKey.Location = New Point(150, 20)
            txtLicenseKey.ReadOnly = True

            ' Create license type label
            lblLicenseType = New Label()
            lblLicenseType.Text = "License Type:"
            lblLicenseType.Font = New Font("Segoe UI", 10)
            lblLicenseType.AutoSize = True
            lblLicenseType.Location = New Point(20, 55)

            ' Create license type textbox
            txtLicenseType = New TextBox()
            txtLicenseType.Width = 350
            txtLicenseType.Height = 25
            txtLicenseType.Font = New Font("Segoe UI", 10)
            txtLicenseType.Location = New Point(150, 55)
            txtLicenseType.ReadOnly = True

            ' Create customer name label
            lblCustomerName = New Label()
            lblCustomerName.Text = "Customer Name:"
            lblCustomerName.Font = New Font("Segoe UI", 10)
            lblCustomerName.AutoSize = True
            lblCustomerName.Location = New Point(20, 90)

            ' Create customer name textbox
            txtCustomerName = New TextBox()
            txtCustomerName.Width = 350
            txtCustomerName.Height = 25
            txtCustomerName.Font = New Font("Segoe UI", 10)
            txtCustomerName.Location = New Point(150, 90)
            txtCustomerName.ReadOnly = True

            ' Create customer email label
            lblCustomerEmail = New Label()
            lblCustomerEmail.Text = "Customer Email:"
            lblCustomerEmail.Font = New Font("Segoe UI", 10)
            lblCustomerEmail.AutoSize = True
            lblCustomerEmail.Location = New Point(20, 125)

            ' Create customer email textbox
            txtCustomerEmail = New TextBox()
            txtCustomerEmail.Width = 350
            txtCustomerEmail.Height = 25
            txtCustomerEmail.Font = New Font("Segoe UI", 10)
            txtCustomerEmail.Location = New Point(150, 125)
            txtCustomerEmail.ReadOnly = True

            ' Create issue date label
            lblIssueDate = New Label()
            lblIssueDate.Text = "Issue Date:"
            lblIssueDate.Font = New Font("Segoe UI", 10)
            lblIssueDate.AutoSize = True
            lblIssueDate.Location = New Point(20, 160)

            ' Create issue date textbox
            txtIssueDate = New TextBox()
            txtIssueDate.Width = 350
            txtIssueDate.Height = 25
            txtIssueDate.Font = New Font("Segoe UI", 10)
            txtIssueDate.Location = New Point(150, 160)
            txtIssueDate.ReadOnly = True

            ' Create expiry date label
            lblExpiryDate = New Label()
            lblExpiryDate.Text = "Expiry Date:"
            lblExpiryDate.Font = New Font("Segoe UI", 10)
            lblExpiryDate.AutoSize = True
            lblExpiryDate.Location = New Point(20, 195)

            ' Create expiry date textbox
            txtExpiryDate = New TextBox()
            txtExpiryDate.Width = 350
            txtExpiryDate.Height = 25
            txtExpiryDate.Font = New Font("Segoe UI", 10)
            txtExpiryDate.Location = New Point(150, 195)
            txtExpiryDate.ReadOnly = True

            ' Create features label
            lblFeatures = New Label()
            lblFeatures.Text = "Features:"
            lblFeatures.Font = New Font("Segoe UI", 10)
            lblFeatures.AutoSize = True
            lblFeatures.Location = New Point(20, 230)

            ' Create features listbox
            lstFeatures = New ListBox()
            lstFeatures.Width = 350
            lstFeatures.Height = 100
            lstFeatures.Font = New Font("Segoe UI", 10)
            lstFeatures.Location = New Point(150, 230)

            ' Create QR code picturebox
            picQRCode = New PictureBox()
            picQRCode.Size = New Size(150, 150)
            picQRCode.Location = New Point(550, 20)
            picQRCode.SizeMode = PictureBoxSizeMode.StretchImage
            picQRCode.BorderStyle = BorderStyle.FixedSingle

            ' Create license card panel
            pnlLicenseCard = New Panel()
            pnlLicenseCard.Size = New Size(350, 200)
            pnlLicenseCard.Location = New Point(400, 230)
            pnlLicenseCard.BorderStyle = BorderStyle.FixedSingle

            ' Create card title label
            lblCardTitle = New Label()
            lblCardTitle.Text = "License Certificate"
            lblCardTitle.Font = New Font("Segoe UI", 12, FontStyle.Bold)
            lblCardTitle.ForeColor = Color.White
            lblCardTitle.AutoSize = True
            lblCardTitle.Location = New Point(10, 10)

            ' Create card customer name label
            lblCardCustomerName = New Label()
            lblCardCustomerName.Text = "Customer: "
            lblCardCustomerName.Font = New Font("Segoe UI", 9)
            lblCardCustomerName.ForeColor = Color.White
            lblCardCustomerName.AutoSize = True
            lblCardCustomerName.Location = New Point(10, 40)

            ' Create card license type label
            lblCardLicenseType = New Label()
            lblCardLicenseType.Text = "Type: "
            lblCardLicenseType.Font = New Font("Segoe UI", 9)
            lblCardLicenseType.ForeColor = Color.White
            lblCardLicenseType.AutoSize = True
            lblCardLicenseType.Location = New Point(10, 60)

            ' Create card expiry date label
            lblCardExpiryDate = New Label()
            lblCardExpiryDate.Text = "Expires: "
            lblCardExpiryDate.Font = New Font("Segoe UI", 9)
            lblCardExpiryDate.ForeColor = Color.White
            lblCardExpiryDate.AutoSize = True
            lblCardExpiryDate.Location = New Point(10, 80)

            ' Create card license key label
            lblCardLicenseKey = New Label()
            lblCardLicenseKey.Text = "Key: "
            lblCardLicenseKey.Font = New Font("Segoe UI", 8)
            lblCardLicenseKey.ForeColor = Color.White
            lblCardLicenseKey.AutoSize = True
            lblCardLicenseKey.Location = New Point(10, 170)

            ' Create card QR code picturebox
            picCardQRCode = New PictureBox()
            picCardQRCode.Size = New Size(80, 80)
            picCardQRCode.Location = New Point(250, 10)
            picCardQRCode.SizeMode = PictureBoxSizeMode.StretchImage

            ' Create print button
            btnPrint = New Button()
            btnPrint.Text = "Print License"
            btnPrint.Width = 150
            btnPrint.Height = 40
            btnPrint.Font = New Font("Segoe UI", 10)
            btnPrint.BackColor = Color.FromArgb(59, 130, 246) ' Blue
            btnPrint.ForeColor = Color.White
            btnPrint.FlatStyle = FlatStyle.Flat
            btnPrint.Location = New Point(20, 500)

            ' Create close button
            btnClose = New Button()
            btnClose.Text = "Close"
            btnClose.Width = 150
            btnClose.Height = 40
            btnClose.Font = New Font("Segoe UI", 10)
            btnClose.BackColor = Color.FromArgb(107, 114, 128) ' Gray
            btnClose.ForeColor = Color.White
            btnClose.FlatStyle = FlatStyle.Flat
            btnClose.Location = New Point(180, 500)

            ' Add controls to header panel
            pnlHeader.Controls.Add(lblTitle)

            ' Add controls to license card panel
            pnlLicenseCard.Controls.Add(lblCardTitle)
            pnlLicenseCard.Controls.Add(lblCardCustomerName)
            pnlLicenseCard.Controls.Add(lblCardLicenseType)
            pnlLicenseCard.Controls.Add(lblCardExpiryDate)
            pnlLicenseCard.Controls.Add(lblCardLicenseKey)
            pnlLicenseCard.Controls.Add(picCardQRCode)

            ' Add controls to content panel
            pnlContent.Controls.Add(lblLicenseKey)
            pnlContent.Controls.Add(txtLicenseKey)
            pnlContent.Controls.Add(lblLicenseType)
            pnlContent.Controls.Add(txtLicenseType)
            pnlContent.Controls.Add(lblCustomerName)
            pnlContent.Controls.Add(txtCustomerName)
            pnlContent.Controls.Add(lblCustomerEmail)
            pnlContent.Controls.Add(txtCustomerEmail)
            pnlContent.Controls.Add(lblIssueDate)
            pnlContent.Controls.Add(txtIssueDate)
            pnlContent.Controls.Add(lblExpiryDate)
            pnlContent.Controls.Add(txtExpiryDate)
            pnlContent.Controls.Add(lblFeatures)
            pnlContent.Controls.Add(lstFeatures)
            pnlContent.Controls.Add(picQRCode)
            pnlContent.Controls.Add(pnlLicenseCard)
            pnlContent.Controls.Add(btnPrint)
            pnlContent.Controls.Add(btnClose)

            ' Add panels to form
            Me.Controls.Add(pnlContent)
            Me.Controls.Add(pnlHeader)
        End Sub

        ''' <summary>
        ''' Handles the form load event
        ''' </summary>
        Private Sub LicenseViewForm_Load(sender As Object, e As EventArgs)
            ' Populate license details
            txtLicenseKey.Text = _license.LicenseKey
            txtLicenseType.Text = _license.LicenseType
            txtCustomerName.Text = _license.CustomerName
            txtCustomerEmail.Text = _license.CustomerEmail
            txtIssueDate.Text = LicenseActivation.Modules.Utilities.FormatDate(_license.IssueDate)
            txtExpiryDate.Text = LicenseActivation.Modules.Utilities.FormatDate(_license.ExpiryDate)

            ' Populate features
            For Each feature In _license.Features
                lstFeatures.Items.Add(feature)
            Next

            ' Generate QR code
            Dim qrCodeData As String = "License Key: " & _license.LicenseKey & vbCrLf &
                                      "Type: " & _license.LicenseType & vbCrLf &
                                      "Customer: " & _license.CustomerName & vbCrLf &
                                      "Expires: " & LicenseActivation.Modules.Utilities.FormatDate(_license.ExpiryDate)

            picQRCode.Image = _qrCodeGenerator.GenerateQRCode(qrCodeData, 150, 150)
            picCardQRCode.Image = _qrCodeGenerator.GenerateQRCode(qrCodeData, 80, 80)

            ' Set license card details
            lblCardCustomerName.Text = "Customer: " & _license.CustomerName
            lblCardLicenseType.Text = "Type: " & _license.LicenseType
            lblCardExpiryDate.Text = "Expires: " & LicenseActivation.Modules.Utilities.FormatDate(_license.ExpiryDate)
            lblCardLicenseKey.Text = "Key: " & _license.LicenseKey

            ' Set license card background based on license type
            Select Case _license.LicenseType.ToLower()
                Case "standard"
                    pnlLicenseCard.BackColor = Color.FromArgb(59, 130, 246) ' Blue
                Case "premium"
                    pnlLicenseCard.BackColor = Color.FromArgb(139, 92, 246) ' Purple
                Case "professional"
                    pnlLicenseCard.BackColor = Color.FromArgb(245, 158, 11) ' Amber
                Case "enterprise"
                    pnlLicenseCard.BackColor = Color.FromArgb(16, 185, 129) ' Green
                Case "platinum"
                    ' Apply platinum gradient
                    pnlLicenseCard.BackColor = Color.FromArgb(229, 228, 226) ' Light platinum
                Case "gold"
                    ' Apply gold gradient
                    pnlLicenseCard.BackColor = Color.FromArgb(255, 215, 0) ' Gold
                Case Else
                    pnlLicenseCard.BackColor = Color.FromArgb(107, 114, 128) ' Gray
            End Select
        End Sub

        ''' <summary>
        ''' Handles the close button click event
        ''' </summary>
        Private Sub BtnClose_Click(sender As Object, e As EventArgs)
            ' Close the form
            Me.Close()
        End Sub

        ''' <summary>
        ''' Handles the print button click event
        ''' </summary>
        Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
            ' Create a printable version of the license card
            Dim printForm As New LicenseActivation.Forms.PrintLicenseForm(_license)
            printForm.ShowDialog()
        End Sub
    End Class
End Namespace