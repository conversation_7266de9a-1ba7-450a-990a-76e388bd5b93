{"ast": null, "code": "import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}", "map": {"version": 3, "names": ["useContext", "nodesToString", "Trans", "TransWithoutContext", "getI18n", "I18nContext", "children", "count", "parent", "i18nKey", "context", "tOptions", "values", "defaults", "components", "ns", "i18n", "i18nFromProps", "t", "tFromProps", "shouldUnescape", "additionalProps", "i18nFromContext", "defaultNS", "defaultNSFromContext", "bind", "options"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/react-i18next/dist/es/Trans.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,EAAEC,KAAK,IAAIC,mBAAmB,QAAQ,0BAA0B;AACtF,SAASC,OAAO,EAAEC,WAAW,QAAQ,cAAc;AACnD,SAASJ,aAAa;AACtB,OAAO,SAASC,KAAKA,CAAC;EACpBI,QAAQ;EACRC,KAAK;EACLC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,QAAQ,GAAG,CAAC,CAAC;EACbC,MAAM;EACNC,QAAQ;EACRC,UAAU;EACVC,EAAE;EACFC,IAAI,EAAEC,aAAa;EACnBC,CAAC,EAAEC,UAAU;EACbC,cAAc;EACd,GAAGC;AACL,CAAC,EAAE;EACD,MAAM;IACJL,IAAI,EAAEM,eAAe;IACrBC,SAAS,EAAEC;EACb,CAAC,GAAGxB,UAAU,CAACK,WAAW,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMW,IAAI,GAAGC,aAAa,IAAIK,eAAe,IAAIlB,OAAO,CAAC,CAAC;EAC1D,MAAMc,CAAC,GAAGC,UAAU,IAAIH,IAAI,EAAEE,CAAC,CAACO,IAAI,CAACT,IAAI,CAAC;EAC1C,OAAOb,mBAAmB,CAAC;IACzBG,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,EAAE,EAAEA,EAAE,IAAIG,CAAC,EAAEH,EAAE,IAAIS,oBAAoB,IAAIR,IAAI,EAAEU,OAAO,EAAEH,SAAS;IACnEP,IAAI;IACJE,CAAC,EAAEC,UAAU;IACbC,cAAc;IACd,GAAGC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}