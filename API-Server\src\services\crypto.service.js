/**
 * Cryptography service for the License Verification API
 * Handles encryption, decryption, and digital signatures
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { setupLogging } = require('../utils/logger');

// Initialize logger
const logger = setupLogging();

// RSA key paths
const PRIVATE_KEY_PATH = process.env.PRIVATE_KEY_PATH || path.join(process.cwd(), 'keys', 'private.pem');
const PUBLIC_KEY_PATH = process.env.PUBLIC_KEY_PATH || path.join(process.cwd(), 'keys', 'public.pem');

// Ensure keys directory exists
const ensureKeysDirectory = () => {
  const keysDir = path.dirname(PRIVATE_KEY_PATH);
  if (!fs.existsSync(keysDir)) {
    fs.mkdirSync(keysDir, { recursive: true });
  }
};

/**
 * Generate RSA key pair if not exists
 * @returns {Promise<void>}
 */
const generateKeyPairIfNotExists = async () => {
  try {
    ensureKeysDirectory();
    
    // Check if keys already exist
    if (fs.existsSync(PRIVATE_KEY_PATH) && fs.existsSync(PUBLIC_KEY_PATH)) {
      logger.info('RSA key pair already exists');
      return;
    }
    
    // Generate new key pair
    logger.info('Generating new RSA key pair...');
    
    return new Promise((resolve, reject) => {
      crypto.generateKeyPair('rsa', {
        modulusLength: 2048,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      }, (err, publicKey, privateKey) => {
        if (err) {
          logger.error(`Failed to generate key pair: ${err.message}`);
          return reject(err);
        }
        
        // Write keys to files
        fs.writeFileSync(PUBLIC_KEY_PATH, publicKey);
        fs.writeFileSync(PRIVATE_KEY_PATH, privateKey);
        
        logger.info('RSA key pair generated successfully');
        resolve();
      });
    });
  } catch (error) {
    logger.error(`Key pair generation error: ${error.message}`);
    throw error;
  }
};

/**
 * Get private key
 * @returns {string} Private key
 */
const getPrivateKey = () => {
  try {
    if (!fs.existsSync(PRIVATE_KEY_PATH)) {
      throw new Error('Private key not found');
    }
    
    return fs.readFileSync(PRIVATE_KEY_PATH, 'utf8');
  } catch (error) {
    logger.error(`Get private key error: ${error.message}`);
    throw error;
  }
};

/**
 * Get public key
 * @returns {string} Public key
 */
const getPublicKey = () => {
  try {
    if (!fs.existsSync(PUBLIC_KEY_PATH)) {
      throw new Error('Public key not found');
    }
    
    return fs.readFileSync(PUBLIC_KEY_PATH, 'utf8');
  } catch (error) {
    logger.error(`Get public key error: ${error.message}`);
    throw error;
  }
};

/**
 * Sign data with private key
 * @param {Object|string} data - Data to sign
 * @returns {string} Base64 encoded signature
 */
const sign = (data) => {
  try {
    const privateKey = getPrivateKey();
    
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : data;
    
    // Create signature
    const sign = crypto.createSign('SHA256');
    sign.update(dataString);
    sign.end();
    
    const signature = sign.sign(privateKey, 'base64');
    return signature;
  } catch (error) {
    logger.error(`Signing error: ${error.message}`);
    throw error;
  }
};

/**
 * Verify signature with public key
 * @param {Object|string} data - Original data
 * @param {string} signature - Base64 encoded signature
 * @returns {boolean} True if signature is valid
 */
const verify = (data, signature) => {
  try {
    const publicKey = getPublicKey();
    
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : data;
    
    // Verify signature
    const verify = crypto.createVerify('SHA256');
    verify.update(dataString);
    verify.end();
    
    return verify.verify(publicKey, signature, 'base64');
  } catch (error) {
    logger.error(`Verification error: ${error.message}`);
    return false;
  }
};

/**
 * Encrypt data with AES-256-GCM
 * @param {Object|string} data - Data to encrypt
 * @param {string} key - Encryption key (32 bytes)
 * @returns {Object} Encrypted data with iv and auth tag
 */
const encrypt = (data, key) => {
  try {
    // Generate random initialization vector
    const iv = crypto.randomBytes(16);
    
    // Create cipher
    const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(key, 'hex'), iv);
    
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : data;
    
    // Encrypt data
    let encrypted = cipher.update(dataString, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Get authentication tag
    const authTag = cipher.getAuthTag().toString('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag
    };
  } catch (error) {
    logger.error(`Encryption error: ${error.message}`);
    throw error;
  }
};

/**
 * Decrypt data with AES-256-GCM
 * @param {Object} encryptedData - Encrypted data object
 * @param {string} key - Encryption key (32 bytes)
 * @returns {string} Decrypted data
 */
const decrypt = (encryptedData, key) => {
  try {
    const { encrypted, iv, authTag } = encryptedData;
    
    // Create decipher
    const decipher = crypto.createDecipheriv(
      'aes-256-gcm', 
      Buffer.from(key, 'hex'), 
      Buffer.from(iv, 'hex')
    );
    
    // Set authentication tag
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    
    // Decrypt data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    logger.error(`Decryption error: ${error.message}`);
    throw error;
  }
};

/**
 * Generate a random encryption key
 * @returns {string} Hex encoded key
 */
const generateEncryptionKey = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Hash data with SHA-256
 * @param {string} data - Data to hash
 * @returns {string} Hex encoded hash
 */
const hash = (data) => {
  return crypto.createHash('sha256').update(data).digest('hex');
};

/**
 * Sign license data for client
 * @param {Object} licenseData - License data to sign
 * @returns {Object} Signed license data
 */
const signLicenseData = async (licenseData) => {
  try {
    // Ensure keys exist
    await generateKeyPairIfNotExists();
    
    // Create signature
    const signature = sign(licenseData);
    
    // Return signed license data
    return {
      ...licenseData,
      signature
    };
  } catch (error) {
    logger.error(`License signing error: ${error.message}`);
    throw error;
  }
};

/**
 * Verify signed license data
 * @param {Object} signedData - Signed license data
 * @param {string} licenseKey - License key for verification
 * @param {string} hardwareFingerprint - Hardware fingerprint for verification
 * @returns {boolean} True if signature is valid
 */
const verifySignature = async (signedData, licenseKey, hardwareFingerprint) => {
  try {
    // Ensure keys exist
    await generateKeyPairIfNotExists();
    
    // Extract signature
    const { signature, ...licenseData } = signedData;
    
    // Verify license key and hardware fingerprint match
    if (licenseData.licenseKey !== licenseKey || licenseData.hardwareFingerprint !== hardwareFingerprint) {
      logger.warn('License key or hardware fingerprint mismatch in signature verification');
      return false;
    }
    
    // Verify signature
    return verify(licenseData, signature);
  } catch (error) {
    logger.error(`Signature verification error: ${error.message}`);
    return false;
  }
};

module.exports = {
  generateKeyPairIfNotExists,
  sign,
  verify,
  encrypt,
  decrypt,
  generateEncryptionKey,
  hash,
  signLicenseData,
  verifySignature
};