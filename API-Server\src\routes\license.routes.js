/**
 * License routes for the License Verification API
 * Handles license activation, verification, and management
 */

const express = require('express');
const router = express.Router();
const licenseController = require('../controllers/license.controller');
const authMiddleware = require('../middleware/auth.middleware');

/**
 * @route POST /api/license/activate
 * @desc Activate a license with hardware fingerprint
 * @access Public
 */
router.post('/activate', licenseController.activateLicense);

/**
 * @route POST /api/license/verify
 * @desc Verify a license is valid and active
 * @access Public
 */
router.post('/verify', licenseController.verifyLicense);

/**
 * @route POST /api/license/revoke
 * @desc Revoke a license
 * @access Protected (Admin only)
 */
router.post('/revoke', authMiddleware.verifyToken, authMiddleware.isAdmin, licenseController.revokeLicense);

/**
 * @route GET /api/license
 * @desc Get all licenses
 * @access Protected (Admin only)
 */
router.get('/', authMiddleware.verifyToken, authMiddleware.isAdmin, licenseController.getAllLicenses);

/**
 * @route GET /api/license/:id
 * @desc Get license by ID
 * @access Protected (Admin only)
 */
router.get('/:id', authMiddleware.verifyToken, authMiddleware.isAdmin, licenseController.getLicenseById);

/**
 * @route POST /api/license
 * @desc Create a new license
 * @access Protected (Admin only)
 */
router.post('/', authMiddleware.verifyToken, authMiddleware.isAdmin, licenseController.createLicense);

/**
 * @route PUT /api/license/:id
 * @desc Update a license
 * @access Protected (Admin only)
 */
router.put('/:id', authMiddleware.verifyToken, authMiddleware.isAdmin, licenseController.updateLicense);

/**
 * @route DELETE /api/license/:id
 * @desc Delete a license
 * @access Protected (Admin only)
 */
router.delete('/:id', authMiddleware.verifyToken, authMiddleware.isAdmin, licenseController.deleteLicense);

module.exports = router;