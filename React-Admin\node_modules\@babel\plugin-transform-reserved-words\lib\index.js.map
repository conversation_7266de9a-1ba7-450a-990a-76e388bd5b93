{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "BindingIdentifier|ReferencedIdentifier", "path", "t", "isValidES3Identifier", "node", "scope", "rename"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-reserved-words\",\n\n    visitor: {\n      \"BindingIdentifier|ReferencedIdentifier\"(path: NodePath<t.Identifier>) {\n        if (!t.isValidES3Identifier(path.node.name)) {\n          path.scope.rename(path.node.name);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAwD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEzC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAEhCC,OAAO,EAAE;MACP,wCAAwCC,CAACC,IAA4B,EAAE;QACrE,IAAI,CAACC,WAAC,CAACC,oBAAoB,CAACF,IAAI,CAACG,IAAI,CAACN,IAAI,CAAC,EAAE;UAC3CG,IAAI,CAACI,KAAK,CAACC,MAAM,CAACL,IAAI,CAACG,IAAI,CAACN,IAAI,CAAC;QACnC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}