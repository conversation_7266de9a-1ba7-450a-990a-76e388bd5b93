export { emitForest, emitForestLines, parseFunctionRanges, parseOffsets } from "./ascii";
export { cloneFunction<PERSON>ov, cloneProcess<PERSON>ov, clone<PERSON><PERSON>ov, cloneRange<PERSON>ov } from "./clone";
export { compareScriptCovs, compareFunctionCovs, compareRangeCovs } from "./compare";
export { mergeFunctionCovs, mergeProcessCovs, mergeScriptCovs } from "./merge";
export { RangeTree } from "./range-tree";

//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIl9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxFQUFFLFVBQVUsRUFBRSxlQUFlLEVBQUUsbUJBQW1CLEVBQUUsWUFBWSxFQUFFLE1BQU0sU0FBUyxDQUFDO0FBQ3pGLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxlQUFlLEVBQUUsY0FBYyxFQUFFLGFBQWEsRUFBRSxNQUFNLFNBQVMsQ0FBQztBQUMzRixPQUFPLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLEVBQUUsZ0JBQWdCLEVBQUUsTUFBTSxXQUFXLENBQUM7QUFDckYsT0FBTyxFQUFFLGlCQUFpQixFQUFFLGdCQUFnQixFQUFFLGVBQWUsRUFBRSxNQUFNLFNBQVMsQ0FBQztBQUMvRSxPQUFPLEVBQUUsU0FBUyxFQUFFLE1BQU0sY0FBYyxDQUFDIiwiZmlsZSI6ImluZGV4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZW1pdEZvcmVzdCwgZW1pdEZvcmVzdExpbmVzLCBwYXJzZUZ1bmN0aW9uUmFuZ2VzLCBwYXJzZU9mZnNldHMgfSBmcm9tIFwiLi9hc2NpaVwiO1xuZXhwb3J0IHsgY2xvbmVGdW5jdGlvbkNvdiwgY2xvbmVQcm9jZXNzQ292LCBjbG9uZVNjcmlwdENvdiwgY2xvbmVSYW5nZUNvdiB9IGZyb20gXCIuL2Nsb25lXCI7XG5leHBvcnQgeyBjb21wYXJlU2NyaXB0Q292cywgY29tcGFyZUZ1bmN0aW9uQ292cywgY29tcGFyZVJhbmdlQ292cyB9IGZyb20gXCIuL2NvbXBhcmVcIjtcbmV4cG9ydCB7IG1lcmdlRnVuY3Rpb25Db3ZzLCBtZXJnZVByb2Nlc3NDb3ZzLCBtZXJnZVNjcmlwdENvdnMgfSBmcm9tIFwiLi9tZXJnZVwiO1xuZXhwb3J0IHsgUmFuZ2VUcmVlIH0gZnJvbSBcIi4vcmFuZ2UtdHJlZVwiO1xuZXhwb3J0IHsgUHJvY2Vzc0NvdiwgU2NyaXB0Q292LCBGdW5jdGlvbkNvdiwgUmFuZ2VDb3YgfSBmcm9tIFwiLi90eXBlc1wiO1xuIl0sInNvdXJjZVJvb3QiOiIifQ==
