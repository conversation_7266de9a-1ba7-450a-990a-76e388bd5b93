/**
 * License service for the License Verification API
 * Handles license generation, validation, and management
 */

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const { setupLogging } = require('../utils/logger');
const { getSupabaseClient } = require('../config/supabase');

// Initialize logger
const logger = setupLogging();

/**
 * Generate a new license key
 * @returns {string} Generated license key
 */
const generateLicenseKey = () => {
  // Generate a random UUID
  const uuid = uuidv4();
  
  // Remove hyphens and convert to uppercase
  const baseKey = uuid.replace(/-/g, '').toUpperCase();
  
  // Split into groups of 5 characters
  const groups = [];
  for (let i = 0; i < baseKey.length; i += 5) {
    groups.push(baseKey.slice(i, i + 5));
  }
  
  // Join groups with hyphens
  return groups.join('-');
};

/**
 * Validate license key format
 * @param {string} licenseKey - License key to validate
 * @returns {boolean} True if license key format is valid
 */
const isValidLicenseKeyFormat = (licenseKey) => {
  // Check if license key matches expected format (5 groups of 5 characters separated by hyphens)
  const licenseKeyRegex = /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/;
  return licenseKeyRegex.test(licenseKey);
};

/**
 * Validate a license key against the database
 * @param {string} licenseKey - License key to validate
 * @returns {Promise<boolean>} True if license key is valid
 */
const validateLicense = async (licenseKey) => {
  try {
    if (!isValidLicenseKeyFormat(licenseKey)) {
      return false;
    }
    
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseKey)
      .eq('is_active', true)
      .single();
    
    if (error || !license) {
      return false;
    }
    
    // Check if license has expired
    if (license.expiration_date && new Date(license.expiration_date) < new Date()) {
      return false;
    }
    
    return true;
  } catch (error) {
    logger.error(`License validation error: ${error.message}`);
    return false;
  }
};

/**
 * Calculate days remaining for a license
 * @param {string} expirationDate - License expiration date
 * @returns {number} Days remaining, or -1 if expired
 */
const calculateDaysRemaining = (expirationDate) => {
  if (!expirationDate) {
    return Infinity; // No expiration date means lifetime license
  }
  
  const expDate = new Date(expirationDate);
  const currentDate = new Date();
  
  // If already expired, return -1
  if (expDate < currentDate) {
    return -1;
  }
  
  // Calculate days remaining
  const diffTime = Math.abs(expDate - currentDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

/**
 * Check if a hardware fingerprint is valid
 * @param {string} hardwareFingerprint - Hardware fingerprint to validate
 * @returns {boolean} True if hardware fingerprint is valid
 */
const isValidHardwareFingerprint = (hardwareFingerprint) => {
  // Hardware fingerprint should be a SHA-256 hash (64 characters)
  return /^[a-f0-9]{64}$/i.test(hardwareFingerprint);
};

/**
 * Get license by hardware fingerprint
 * @param {string} hardwareFingerprint - Hardware fingerprint to search for
 * @returns {Promise<Object|null>} License object or null if not found
 */
const getLicenseByHardwareFingerprint = async (hardwareFingerprint) => {
  try {
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('hardware_fingerprint', hardwareFingerprint)
      .eq('is_active', true)
      .single();
    
    if (error || !license) {
      return null;
    }
    
    return license;
  } catch (error) {
    logger.error(`Get license by hardware fingerprint error: ${error.message}`);
    return null;
  }
};

/**
 * Check if a license has specific permission
 * @param {Object} license - License object
 * @param {string} permission - Permission to check
 * @returns {boolean} True if license has the permission
 */
const hasPermission = (license, permission) => {
  if (!license || !license.permissions) {
    return false;
  }
  
  // If permissions is a string, parse it as JSON
  const permissions = typeof license.permissions === 'string' 
    ? JSON.parse(license.permissions) 
    : license.permissions;
  
  return permissions.includes(permission);
};

/**
 * Get all active licenses
 * @returns {Promise<Array>} Array of active licenses
 */
const getAllActiveLicenses = async () => {
  try {
    const supabase = getSupabaseClient();
    const { data: licenses, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('is_active', true);
    
    if (error) {
      logger.error(`Get all active licenses error: ${error.message}`);
      return [];
    }
    
    return licenses;
  } catch (error) {
    logger.error(`Get all active licenses error: ${error.message}`);
    return [];
  }
};

/**
 * Get licenses expiring soon
 * @param {number} days - Number of days to check
 * @returns {Promise<Array>} Array of licenses expiring within the specified days
 */
const getLicensesExpiringSoon = async (days = 7) => {
  try {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    
    const supabase = getSupabaseClient();
    const { data: licenses, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('is_active', true)
      .lt('expiration_date', futureDate.toISOString())
      .gt('expiration_date', new Date().toISOString());
    
    if (error) {
      logger.error(`Get licenses expiring soon error: ${error.message}`);
      return [];
    }
    
    return licenses;
  } catch (error) {
    logger.error(`Get licenses expiring soon error: ${error.message}`);
    return [];
  }
};

module.exports = {
  generateLicenseKey,
  isValidLicenseKeyFormat,
  validateLicense,
  calculateDaysRemaining,
  isValidHardwareFingerprint,
  getLicenseByHardwareFingerprint,
  hasPermission,
  getAllActiveLicenses,
  getLicensesExpiringSoon
};