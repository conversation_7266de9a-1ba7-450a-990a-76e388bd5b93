Imports System
Imports System.Drawing
Imports QRCoder

Namespace LicenseActivation.Classes
    ''' <summary>
    ''' Provides methods to generate QR codes
    ''' </summary>
    Public Class QRCodeGenerator
        ''' <summary>
        ''' Generates a QR code image from the given text
        ''' </summary>
        ''' <param name="text">The text to encode in the QR code</param>
        ''' <param name="size">The size of the QR code image</param>
        ''' <returns>A Bitmap containing the QR code</returns>
        Public Shared Function GenerateQRCode(text As String, size As Integer) As Bitmap
            ' Create QR code generator
            Using qrGenerator As New QRCoder.QRCodeGenerator()
                ' Generate QR code data
                Dim qrCodeData As QRCodeData = qrGenerator.CreateQrCode(text, QRCoder.QRCodeGenerator.ECCLevel.Q)

                ' Create QR code
                Using qrCode As New QRCode(qrCodeData)
                    ' Get QR code as bitmap
                    Return qrCode.GetGraphic(size, Color.Black, Color.White, True)
                End Using
            End Using
        End Function

        ''' <summary>
        ''' Generates a QR code image from the given text with specified width and height
        ''' </summary>
        ''' <param name="text">The text to encode in the QR code</param>
        ''' <param name="width">The width of the QR code image</param>
        ''' <param name="height">The height of the QR code image</param>
        ''' <returns>A Bitmap containing the QR code</returns>
        Public Shared Function GenerateQRCode(text As String, width As Integer, height As Integer) As Bitmap
            ' Use the smaller dimension to ensure the QR code fits properly
            Dim size As Integer = Math.Min(width, height)
            Return GenerateQRCode(text, size)
        End Function

        ''' <summary>
        ''' Generates a QR code image with a logo in the center
        ''' </summary>
        ''' <param name="text">The text to encode in the QR code</param>
        ''' <param name="logo">The logo to place in the center of the QR code</param>
        ''' <param name="size">The size of the QR code image</param>
        ''' <returns>A Bitmap containing the QR code with logo</returns>
        Public Shared Function GenerateQRCodeWithLogo(text As String, logo As Bitmap, size As Integer) As Bitmap
            ' Create QR code generator
            Using qrGenerator As New QRCoder.QRCodeGenerator()
                ' Generate QR code data
                Dim qrCodeData As QRCodeData = qrGenerator.CreateQrCode(text, QRCoder.QRCodeGenerator.ECCLevel.Q)

                ' Create QR code
                Using qrCode As New QRCode(qrCodeData)
                    ' Get QR code as bitmap with logo
                    Return qrCode.GetGraphic(size, Color.Black, Color.White, logo, 15, 10, True)
                End Using
            End Using
        End Function

        ''' <summary>
        ''' Generates a QR code image with custom colors
        ''' </summary>
        ''' <param name="text">The text to encode in the QR code</param>
        ''' <param name="size">The size of the QR code image</param>
        ''' <param name="darkColor">The color for the dark modules</param>
        ''' <param name="lightColor">The color for the light modules</param>
        ''' <returns>A Bitmap containing the QR code with custom colors</returns>
        Public Shared Function GenerateQRCodeWithColors(text As String, size As Integer, darkColor As Color, lightColor As Color) As Bitmap
            ' Create QR code generator
            Using qrGenerator As New QRCoder.QRCodeGenerator()
                ' Generate QR code data
                Dim qrCodeData As QRCodeData = qrGenerator.CreateQrCode(text, QRCoder.QRCodeGenerator.ECCLevel.Q)

                ' Create QR code
                Using qrCode As New QRCode(qrCodeData)
                    ' Get QR code as bitmap with custom colors
                    Return qrCode.GetGraphic(size, darkColor, lightColor, True)
                End Using
            End Using
        End Function
    End Class
End Namespace