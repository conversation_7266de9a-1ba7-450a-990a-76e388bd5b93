/**
 * Telegram service for the License Verification API
 * Handles sending notifications to Telegram
 */

const axios = require('axios');
const { setupLogging } = require('../utils/logger');

// Initialize logger
const logger = setupLogging();

/**
 * Send message to Telegram
 * @param {string} message - Message to send
 * @returns {Promise<Object>} Telegram API response
 */
const sendMessage = async (message) => {
  try {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const chatId = process.env.TELEGRAM_CHAT_ID;
    
    if (!botToken || !chatId) {
      logger.warn('Telegram bot token or chat ID not configured');
      return null;
    }
    
    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
    
    const response = await axios.post(url, {
      chat_id: chatId,
      text: message,
      parse_mode: 'Markdown'
    });
    
    logger.info(`Telegram message sent successfully: ${message.substring(0, 50)}...`);
    return response.data;
  } catch (error) {
    logger.error(`Failed to send Telegram message: ${error.message}`);
    return null;
  }
};

/**
 * Send security alert to Telegram
 * @param {string} alertType - Type of security alert
 * @param {Object} data - Alert data
 * @returns {Promise<Object>} Telegram API response
 */
const sendSecurityAlert = async (alertType, data) => {
  try {
    const message = `🚨 *SECURITY ALERT: ${alertType}* 🚨\n\n` +
      `*License:* ${data.licenseKey || 'N/A'}\n` +
      `*Client IP:* ${data.ip || 'N/A'}\n` +
      `*Time:* ${new Date().toISOString()}\n` +
      `${data.hardwareFingerprint ? `*Hardware:* ${data.hardwareFingerprint.substring(0, 10)}...\n` : ''}` +
      `${data.details ? `*Details:* ${data.details}\n` : ''}` +
      `${data.clientInfo ? `*Client:* ${JSON.stringify(data.clientInfo)}\n` : ''}` +
      `${data.isSuspicious ? '⚠️ *SUSPICIOUS ALERT - License not found in database*' : ''}`;
    
    return await sendMessage(message);
  } catch (error) {
    logger.error(`Failed to send security alert to Telegram: ${error.message}`);
    return null;
  }
};

/**
 * Send license expiration notification to Telegram
 * @param {Object} license - License data
 * @param {number} daysRemaining - Days remaining until expiration
 * @returns {Promise<Object>} Telegram API response
 */
const sendExpirationNotification = async (license, daysRemaining) => {
  try {
    const message = `📅 *LICENSE EXPIRATION NOTICE* 📅\n\n` +
      `*License:* ${license.license_key}\n` +
      `*Customer:* ${license.customer_name}\n` +
      `*Email:* ${license.customer_email}\n` +
      `*Type:* ${license.customer_type}\n` +
      `*Expires:* ${license.expiration_date}\n` +
      `*Days Remaining:* ${daysRemaining}\n\n` +
      `This license will expire soon. Please take action if renewal is needed.`;
    
    return await sendMessage(message);
  } catch (error) {
    logger.error(`Failed to send expiration notification to Telegram: ${error.message}`);
    return null;
  }
};

/**
 * Send license activation notification to Telegram
 * @param {Object} license - License data
 * @param {Object} activationData - Activation data
 * @returns {Promise<Object>} Telegram API response
 */
const sendActivationNotification = async (license, activationData) => {
  try {
    const message = `✅ *NEW LICENSE ACTIVATION* ✅\n\n` +
      `*License:* ${license.license_key}\n` +
      `*Customer:* ${license.customer_name}\n` +
      `*Email:* ${license.customer_email}\n` +
      `*Type:* ${license.customer_type}\n` +
      `*IP Address:* ${activationData.ip || 'N/A'}\n` +
      `*Hardware ID:* ${activationData.hardwareFingerprint ? activationData.hardwareFingerprint.substring(0, 10) + '...' : 'N/A'}\n` +
      `*Time:* ${new Date().toISOString()}`;
    
    return await sendMessage(message);
  } catch (error) {
    logger.error(`Failed to send activation notification to Telegram: ${error.message}`);
    return null;
  }
};

module.exports = {
  sendMessage,
  sendSecurityAlert,
  sendExpirationNotification,
  sendActivationNotification
};