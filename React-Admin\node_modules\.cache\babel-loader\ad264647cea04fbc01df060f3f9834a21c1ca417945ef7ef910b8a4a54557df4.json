{"ast": null, "code": "import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport const withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    useSSR(initialI18nStore, initialLanguage);\n    return createElement(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};", "map": {"version": 3, "names": ["createElement", "useSSR", "composeInitialProps", "getDisplayName", "withSSR", "Extend", "WrappedComponent", "I18nextWithSSR", "initialI18nStore", "initialLanguage", "rest", "getInitialProps", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/react-i18next/dist/es/withSSR.js"], "sourcesContent": ["import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport const withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    useSSR(initialI18nStore, initialLanguage);\n    return createElement(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM,SAASC,MAAMA,CAACC,gBAAgB,EAAE;EAC7D,SAASC,cAAcA,CAAC;IACtBC,gBAAgB;IAChBC,eAAe;IACf,GAAGC;EACL,CAAC,EAAE;IACDT,MAAM,CAACO,gBAAgB,EAAEC,eAAe,CAAC;IACzC,OAAOT,aAAa,CAACM,gBAAgB,EAAE;MACrC,GAAGI;IACL,CAAC,CAAC;EACJ;EACAH,cAAc,CAACI,eAAe,GAAGT,mBAAmB,CAACI,gBAAgB,CAAC;EACtEC,cAAc,CAACK,WAAW,GAAG,kBAAkBT,cAAc,CAACG,gBAAgB,CAAC,GAAG;EAClFC,cAAc,CAACD,gBAAgB,GAAGA,gBAAgB;EAClD,OAAOC,cAAc;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}