{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "arr", "each", "for<PERSON>ach", "slice", "defaults", "obj", "call", "arguments", "source", "prop", "undefined", "hasXMLHttpRequest", "XMLHttpRequest", "isPromise", "<PERSON><PERSON><PERSON><PERSON>", "then", "makePromise", "Promise", "resolve"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/i18next-http-backend/esm/utils.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,IAAIK,GAAG,GAAG,EAAE;AACZ,IAAIC,IAAI,GAAGD,GAAG,CAACE,OAAO;AACtB,IAAIC,KAAK,GAAGH,GAAG,CAACG,KAAK;AACrB,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5BJ,IAAI,CAACK,IAAI,CAACH,KAAK,CAACG,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC,EAAE,UAAUC,MAAM,EAAE;IACpD,IAAIA,MAAM,EAAE;MACV,KAAK,IAAIC,IAAI,IAAID,MAAM,EAAE;QACvB,IAAIH,GAAG,CAACI,IAAI,CAAC,KAAKC,SAAS,EAAEL,GAAG,CAACI,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ;AACA,OAAO,SAASM,iBAAiBA,CAAA,EAAG;EAClC,OAAO,OAAOC,cAAc,KAAK,UAAU,IAAI,CAAC,OAAOA,cAAc,KAAK,WAAW,GAAG,WAAW,GAAGlB,OAAO,CAACkB,cAAc,CAAC,MAAM,QAAQ;AAC7I;AACA,SAASC,SAASA,CAACC,YAAY,EAAE;EAC/B,OAAO,CAAC,CAACA,YAAY,IAAI,OAAOA,YAAY,CAACC,IAAI,KAAK,UAAU;AAClE;AACA,OAAO,SAASC,WAAWA,CAACF,YAAY,EAAE;EACxC,IAAID,SAAS,CAACC,YAAY,CAAC,EAAE;IAC3B,OAAOA,YAAY;EACrB;EACA,OAAOG,OAAO,CAACC,OAAO,CAACJ,YAAY,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}