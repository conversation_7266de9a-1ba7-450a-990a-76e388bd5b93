import O,{createContext as J,useContext as V,useEffect as se,useMemo as D,useReducer as ue,useRef as j}from"react";import{Description as de,useDescriptions as X}from'../../components/description/description.js';import{Keys as _}from'../../components/keyboard.js';import{Label as ce,useLabels as q}from'../../components/label/label.js';import{useControllable as fe}from'../../hooks/use-controllable.js';import{useDisposables as Te}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useFlags as me}from'../../hooks/use-flags.js';import{useId as Q}from'../../hooks/use-id.js';import{useIsoMorphicEffect as ye}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Re}from'../../hooks/use-latest-value.js';import{useSyncRefs as Y}from'../../hooks/use-sync-refs.js';import{useTreeWalker as be}from'../../hooks/use-tree-walker.js';import{Features as ge,Hidden as Oe}from'../../internal/hidden.js';import{isDisabledReactIssue7711 as Z}from'../../utils/bugs.js';import{Focus as S,focusIn as z,FocusResult as ee,sortByDomNode as Ee}from'../../utils/focus-management.js';import{attemptSubmit as ve,objectToFormEntries as Pe}from'../../utils/form.js';import{match as Ae}from'../../utils/match.js';import{getOwnerDocument as De}from'../../utils/owner.js';import{compact as _e,forwardRefWithAs as te,render as re}from'../../utils/render.js';var Ge=(t=>(t[t.RegisterOption=0]="RegisterOption",t[t.UnregisterOption=1]="UnregisterOption",t))(Ge||{});let Ce={[0](o,r){let t=[...o.options,{id:r.id,element:r.element,propsRef:r.propsRef}];return{...o,options:Ee(t,p=>p.element.current)}},[1](o,r){let t=o.options.slice(),p=o.options.findIndex(T=>T.id===r.id);return p===-1?o:(t.splice(p,1),{...o,options:t})}},B=J(null);B.displayName="RadioGroupDataContext";function oe(o){let r=V(B);if(r===null){let t=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return r}let $=J(null);$.displayName="RadioGroupActionsContext";function ne(o){let r=V($);if(r===null){let t=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ne),t}return r}function ke(o,r){return Ae(r.type,Ce,o,r)}let Le="div";function he(o,r){let t=Q(),{id:p=`headlessui-radiogroup-${t}`,value:T,defaultValue:v,form:M,name:m,onChange:H,by:G=(e,i)=>e===i,disabled:P=!1,...N}=o,y=E(typeof G=="string"?(e,i)=>{let n=G;return(e==null?void 0:e[n])===(i==null?void 0:i[n])}:G),[A,L]=ue(ke,{options:[]}),a=A.options,[h,R]=q(),[C,U]=X(),k=j(null),W=Y(k,r),[l,s]=fe(T,H,v),b=D(()=>a.find(e=>!e.propsRef.current.disabled),[a]),x=D(()=>a.some(e=>y(e.propsRef.current.value,l)),[a,l]),d=E(e=>{var n;if(P||y(e,l))return!1;let i=(n=a.find(f=>y(f.propsRef.current.value,e)))==null?void 0:n.propsRef.current;return i!=null&&i.disabled?!1:(s==null||s(e),!0)});be({container:k.current,accept(e){return e.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});let F=E(e=>{let i=k.current;if(!i)return;let n=De(i),f=a.filter(u=>u.propsRef.current.disabled===!1).map(u=>u.element.current);switch(e.key){case _.Enter:ve(e.currentTarget);break;case _.ArrowLeft:case _.ArrowUp:if(e.preventDefault(),e.stopPropagation(),z(f,S.Previous|S.WrapAround)===ee.Success){let g=a.find(K=>K.element.current===(n==null?void 0:n.activeElement));g&&d(g.propsRef.current.value)}break;case _.ArrowRight:case _.ArrowDown:if(e.preventDefault(),e.stopPropagation(),z(f,S.Next|S.WrapAround)===ee.Success){let g=a.find(K=>K.element.current===(n==null?void 0:n.activeElement));g&&d(g.propsRef.current.value)}break;case _.Space:{e.preventDefault(),e.stopPropagation();let u=a.find(g=>g.element.current===(n==null?void 0:n.activeElement));u&&d(u.propsRef.current.value)}break}}),c=E(e=>(L({type:0,...e}),()=>L({type:1,id:e.id}))),w=D(()=>({value:l,firstOption:b,containsCheckedOption:x,disabled:P,compare:y,...A}),[l,b,x,P,y,A]),ie=D(()=>({registerOption:c,change:d}),[c,d]),ae={ref:W,id:p,role:"radiogroup","aria-labelledby":h,"aria-describedby":C,onKeyDown:F},pe=D(()=>({value:l}),[l]),I=j(null),le=Te();return se(()=>{I.current&&v!==void 0&&le.addEventListener(I.current,"reset",()=>{d(v)})},[I,d]),O.createElement(U,{name:"RadioGroup.Description"},O.createElement(R,{name:"RadioGroup.Label"},O.createElement($.Provider,{value:ie},O.createElement(B.Provider,{value:w},m!=null&&l!=null&&Pe({[m]:l}).map(([e,i],n)=>O.createElement(Oe,{features:ge.Hidden,ref:n===0?f=>{var u;I.current=(u=f==null?void 0:f.closest("form"))!=null?u:null}:void 0,..._e({key:e,as:"input",type:"radio",checked:i!=null,hidden:!0,readOnly:!0,form:M,disabled:P,name:e,value:i})})),re({ourProps:ae,theirProps:N,slot:pe,defaultTag:Le,name:"RadioGroup"})))))}var xe=(t=>(t[t.Empty=1]="Empty",t[t.Active=2]="Active",t))(xe||{});let Fe="div";function we(o,r){var F;let t=Q(),{id:p=`headlessui-radiogroup-option-${t}`,value:T,disabled:v=!1,...M}=o,m=j(null),H=Y(m,r),[G,P]=q(),[N,y]=X(),{addFlag:A,removeFlag:L,hasFlag:a}=me(1),h=Re({value:T,disabled:v}),R=oe("RadioGroup.Option"),C=ne("RadioGroup.Option");ye(()=>C.registerOption({id:p,element:m,propsRef:h}),[p,C,m,h]);let U=E(c=>{var w;if(Z(c.currentTarget))return c.preventDefault();C.change(T)&&(A(2),(w=m.current)==null||w.focus())}),k=E(c=>{if(Z(c.currentTarget))return c.preventDefault();A(2)}),W=E(()=>L(2)),l=((F=R.firstOption)==null?void 0:F.id)===p,s=R.disabled||v,b=R.compare(R.value,T),x={ref:H,id:p,role:"radio","aria-checked":b?"true":"false","aria-labelledby":G,"aria-describedby":N,"aria-disabled":s?!0:void 0,tabIndex:(()=>s?-1:b||!R.containsCheckedOption&&l?0:-1)(),onClick:s?void 0:U,onFocus:s?void 0:k,onBlur:s?void 0:W},d=D(()=>({checked:b,disabled:s,active:a(2)}),[b,s,a]);return O.createElement(y,{name:"RadioGroup.Description"},O.createElement(P,{name:"RadioGroup.Label"},re({ourProps:x,theirProps:M,slot:d,defaultTag:Fe,name:"RadioGroup.Option"})))}let Ie=te(he),Se=te(we),it=Object.assign(Ie,{Option:Se,Label:ce,Description:de});export{it as RadioGroup};
