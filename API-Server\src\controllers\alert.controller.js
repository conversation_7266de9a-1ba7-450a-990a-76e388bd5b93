/**
 * Alert controller for the License Verification API
 * Handles security alerts and notifications
 */

const { setupLogging, logSecurityEvent } = require('../utils/logger');
const { getSupabaseClient } = require('../config/supabase');
const telegramService = require('../services/telegram.service');
const licenseService = require('../services/license.service');

// Initialize logger
const logger = setupLogging();

/**
 * Send security alert (tampering, debugging, etc.)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendSecurityAlert = async (req, res) => {
  try {
    const { 
      licenseKey, 
      hardwareFingerprint, 
      alertType, 
      details,
      clientInfo 
    } = req.body;
    
    if (!licenseKey || !alertType) {
      return res.status(400).json({ error: 'License key and alert type are required' });
    }
    
    // Get license from database
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseKey)
      .single();
    
    // Even if license is not found, still log the alert but mark it as suspicious
    const isSuspicious = error || !license;
    
    // Log security event
    logSecurityEvent(logger, alertType, { 
      licenseKey, 
      hardwareFingerprint,
      details,
      clientInfo,
      ip: req.ip,
      isSuspicious
    });
    
    // Store alert in database
    const { data: alert, insertError } = await supabase
      .from('security_alerts')
      .insert({
        license_id: license?.id,
        license_key: licenseKey,
        hardware_fingerprint: hardwareFingerprint,
        alert_type: alertType,
        details,
        client_info: clientInfo,
        client_ip: req.ip,
        is_suspicious: isSuspicious,
        alert_date: new Date().toISOString()
      })
      .select()
      .single();
    
    if (insertError) {
      logger.error(`Failed to store security alert: ${insertError.message}`);
    }
    
    // Send Telegram notification
    if (process.env.TELEGRAM_NOTIFICATIONS_ENABLED === 'true') {
      const message = `🚨 SECURITY ALERT 🚨\n\n` +
        `Type: ${alertType}\n` +
        `License: ${licenseKey}\n` +
        `Client IP: ${req.ip}\n` +
        `Time: ${new Date().toISOString()}\n` +
        `${clientInfo ? `Client: ${JSON.stringify(clientInfo)}\n` : ''}` +
        `${details ? `Details: ${details}\n` : ''}` +
        `${isSuspicious ? '⚠️ SUSPICIOUS ALERT - License not found in database' : ''}`;
      
      await telegramService.sendMessage(message);
    }
    
    // Return success even if license not found to avoid giving away information
    return res.status(200).json({
      success: true,
      message: 'Alert received'
    });
  } catch (error) {
    logger.error(`Security alert error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Send alert to Telegram
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendTelegramAlert = async (req, res) => {
  try {
    const { 
      licenseKey, 
      message, 
      alertType,
      clientInfo 
    } = req.body;
    
    if (!licenseKey || !message) {
      return res.status(400).json({ error: 'License key and message are required' });
    }
    
    // Validate license key
    const isValidLicense = await licenseService.validateLicense(licenseKey);
    if (!isValidLicense) {
      logSecurityEvent(logger, 'INVALID_LICENSE_TELEGRAM_ALERT', { licenseKey, ip: req.ip });
      return res.status(403).json({ error: 'Invalid license key' });
    }
    
    // Format Telegram message
    const telegramMessage = `📢 ALERT: ${alertType || 'GENERAL'}\n\n` +
      `License: ${licenseKey}\n` +
      `Message: ${message}\n` +
      `Client IP: ${req.ip}\n` +
      `Time: ${new Date().toISOString()}\n` +
      `${clientInfo ? `Client: ${JSON.stringify(clientInfo)}\n` : ''}`;
    
    // Send to Telegram
    if (process.env.TELEGRAM_NOTIFICATIONS_ENABLED === 'true') {
      await telegramService.sendMessage(telegramMessage);
    } else {
      logger.info(`Telegram notification would be sent (disabled): ${telegramMessage}`);
    }
    
    // Log alert
    const supabase = getSupabaseClient();
    await supabase
      .from('telegram_alerts')
      .insert({
        license_key: licenseKey,
        message,
        alert_type: alertType || 'GENERAL',
        client_info: clientInfo,
        client_ip: req.ip,
        alert_date: new Date().toISOString()
      });
    
    return res.status(200).json({
      success: true,
      message: 'Alert sent successfully'
    });
  } catch (error) {
    logger.error(`Telegram alert error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Get alert history
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAlertHistory = async (req, res) => {
  try {
    const { licenseKey, alertType, startDate, endDate, limit } = req.query;
    
    // Build query
    const supabase = getSupabaseClient();
    let query = supabase
      .from('security_alerts')
      .select('*')
      .order('alert_date', { ascending: false });
    
    // Apply filters
    if (licenseKey) {
      query = query.eq('license_key', licenseKey);
    }
    
    if (alertType) {
      query = query.eq('alert_type', alertType);
    }
    
    if (startDate) {
      query = query.gte('alert_date', startDate);
    }
    
    if (endDate) {
      query = query.lte('alert_date', endDate);
    }
    
    // Apply limit
    if (limit) {
      query = query.limit(parseInt(limit));
    } else {
      query = query.limit(100); // Default limit
    }
    
    // Execute query
    const { data: securityAlerts, error: securityError } = await query;
    
    // Get Telegram alerts
    let telegramQuery = supabase
      .from('telegram_alerts')
      .select('*')
      .order('alert_date', { ascending: false });
    
    // Apply filters to Telegram alerts
    if (licenseKey) {
      telegramQuery = telegramQuery.eq('license_key', licenseKey);
    }
    
    if (alertType) {
      telegramQuery = telegramQuery.eq('alert_type', alertType);
    }
    
    if (startDate) {
      telegramQuery = telegramQuery.gte('alert_date', startDate);
    }
    
    if (endDate) {
      telegramQuery = telegramQuery.lte('alert_date', endDate);
    }
    
    // Apply limit
    if (limit) {
      telegramQuery = telegramQuery.limit(parseInt(limit));
    } else {
      telegramQuery = telegramQuery.limit(100); // Default limit
    }
    
    // Execute Telegram query
    const { data: telegramAlerts, error: telegramError } = await telegramQuery;
    
    if (securityError) {
      logger.error(`Error fetching security alerts: ${securityError.message}`);
    }
    
    if (telegramError) {
      logger.error(`Error fetching telegram alerts: ${telegramError.message}`);
    }
    
    // Combine and sort alerts
    const allAlerts = [
      ...(securityAlerts || []).map(alert => ({ ...alert, alertSource: 'security' })),
      ...(telegramAlerts || []).map(alert => ({ ...alert, alertSource: 'telegram' }))
    ].sort((a, b) => new Date(b.alert_date) - new Date(a.alert_date));
    
    // Apply final limit if both queries returned results
    const finalAlerts = limit ? allAlerts.slice(0, parseInt(limit)) : allAlerts;
    
    return res.status(200).json({
      success: true,
      alerts: finalAlerts
    });
  } catch (error) {
    logger.error(`Get alert history error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Get alert statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAlertStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const supabase = getSupabaseClient();
    
    // Build query for security alerts count by type
    let securityQuery = supabase
      .from('security_alerts')
      .select('alert_type, count', { count: 'exact' })
      .order('count', { ascending: false })
      .group('alert_type');
    
    // Apply date filters
    if (startDate) {
      securityQuery = securityQuery.gte('alert_date', startDate);
    }
    
    if (endDate) {
      securityQuery = securityQuery.lte('alert_date', endDate);
    }
    
    // Execute security alerts query
    const { data: securityAlertsByType, error: securityError } = await securityQuery;
    
    // Build query for telegram alerts count by type
    let telegramQuery = supabase
      .from('telegram_alerts')
      .select('alert_type, count', { count: 'exact' })
      .order('count', { ascending: false })
      .group('alert_type');
    
    // Apply date filters
    if (startDate) {
      telegramQuery = telegramQuery.gte('alert_date', startDate);
    }
    
    if (endDate) {
      telegramQuery = telegramQuery.lte('alert_date', endDate);
    }
    
    // Execute telegram alerts query
    const { data: telegramAlertsByType, error: telegramError } = await telegramQuery;
    
    // Get total counts
    let securityCountQuery = supabase
      .from('security_alerts')
      .select('*', { count: 'exact', head: true });
    
    let telegramCountQuery = supabase
      .from('telegram_alerts')
      .select('*', { count: 'exact', head: true });
    
    // Apply date filters to count queries
    if (startDate) {
      securityCountQuery = securityCountQuery.gte('alert_date', startDate);
      telegramCountQuery = telegramCountQuery.gte('alert_date', startDate);
    }
    
    if (endDate) {
      securityCountQuery = securityCountQuery.lte('alert_date', endDate);
      telegramCountQuery = telegramCountQuery.lte('alert_date', endDate);
    }
    
    // Execute count queries
    const { count: securityCount, error: securityCountError } = await securityCountQuery;
    const { count: telegramCount, error: telegramCountError } = await telegramCountQuery;
    
    // Get recent alerts
    const { data: recentAlerts, error: recentError } = await supabase
      .from('security_alerts')
      .select('*')
      .order('alert_date', { ascending: false })
      .limit(10);
    
    if (securityError || telegramError || securityCountError || telegramCountError || recentError) {
      logger.error('Error fetching alert statistics');
    }
    
    return res.status(200).json({
      success: true,
      statistics: {
        totalAlerts: (securityCount || 0) + (telegramCount || 0),
        securityAlerts: securityCount || 0,
        telegramAlerts: telegramCount || 0,
        securityAlertsByType: securityAlertsByType || [],
        telegramAlertsByType: telegramAlertsByType || [],
        recentAlerts: recentAlerts || []
      }
    });
  } catch (error) {
    logger.error(`Get alert stats error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Delete an alert
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteAlert = async (req, res) => {
  try {
    const { id } = req.params;
    const { alertSource } = req.query;
    
    if (!id || !alertSource) {
      return res.status(400).json({ error: 'Alert ID and source are required' });
    }
    
    const supabase = getSupabaseClient();
    let error;
    
    if (alertSource === 'security') {
      const { error: deleteError } = await supabase
        .from('security_alerts')
        .delete()
        .eq('id', id);
      
      error = deleteError;
    } else if (alertSource === 'telegram') {
      const { error: deleteError } = await supabase
        .from('telegram_alerts')
        .delete()
        .eq('id', id);
      
      error = deleteError;
    } else {
      return res.status(400).json({ error: 'Invalid alert source' });
    }
    
    if (error) {
      logger.error(`Delete alert error: ${error.message}`);
      return res.status(500).json({ error: 'Failed to delete alert' });
    }
    
    logger.info(`Alert ${id} deleted by ${req.user.email}`);
    
    return res.status(200).json({
      success: true,
      message: 'Alert deleted successfully'
    });
  } catch (error) {
    logger.error(`Delete alert error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  sendSecurityAlert,
  sendTelegramAlert,
  getAlertHistory,
  getAlertStats,
  deleteAlert
};