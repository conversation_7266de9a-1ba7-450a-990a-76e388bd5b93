<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <!-- <StartupObject>LicenseActivation.Program</StartupObject> -->
    <UseWindowsForms>true</UseWindowsForms>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>Resources\app_icon.ico</ApplicationIcon>
    <RootNamespace>LicenseActivation</RootNamespace>
    <AssemblyName>LicenseActivation</AssemblyName>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>

  </PropertyGroup>

  <ItemGroup>
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QRCoder" Version="1.4.3" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="My Project\Resources.Designer.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>