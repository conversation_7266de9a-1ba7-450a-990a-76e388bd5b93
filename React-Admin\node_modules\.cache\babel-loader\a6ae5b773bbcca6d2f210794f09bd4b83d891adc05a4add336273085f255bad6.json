{"ast": null, "code": "import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      warn(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      warn(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    warn(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(children) && Object.hasOwnProperty.call(children, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = cloneElement(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return createElement(Fragment, null, comp);\n  }\n  return createElement(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if (isObject(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  warnOnce(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  const content = renderNodes(generatedComponents || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}", "map": {"version": 3, "names": ["Fragment", "isValidElement", "cloneElement", "createElement", "Children", "HTML", "isObject", "isString", "warn", "warnOnce", "getDefaults", "getI18n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "checkLength", "base", "props", "children", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18nIsDynamicList", "getAsArray", "hasValidReactChildren", "Array", "isArray", "every", "data", "mergeProps", "source", "target", "newTarget", "Object", "assign", "nodesToString", "i18nOptions", "i18n", "i18nKey", "stringNode", "childrenA<PERSON>y", "keepArray", "transSupportBasicHtmlNodes", "transKeepBasicHtmlNodesFor", "for<PERSON>ach", "child", "childIndex", "type", "childPropsCount", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "format", "clone", "value", "renderNodes", "targetString", "combinedTOpts", "shouldUnescape", "emptyChildrenButNeedsHandling", "RegExp", "map", "keep", "join", "test", "getData", "childs", "ast", "parse", "opts", "renderInner", "rootReactNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapAST", "pushTranslatedJSX", "inner", "mem", "i", "isVoid", "dummy", "push", "key", "undefined", "c", "ref", "reactNode", "astNode", "reactNodes", "astNodes", "reduce", "translationContent", "services", "interpolator", "interpolate", "language", "tmp", "parseInt", "name", "attrs", "isElement", "isValidTranslationWithChildren", "voidElement", "isEmptyTransWithHTML", "isKnownComponent", "hasOwnProperty", "call", "Number", "isNaN", "parseFloat", "wrapTextNodes", "transWrapTextNodes", "unescape", "result", "fixComponentProps", "component", "index", "translation", "componentKey", "comp", "Componentized", "generateArrayComponents", "components", "generateObjectComponents", "componentMap", "generateComponents", "Trans", "count", "parent", "context", "tOptions", "values", "defaults", "ns", "i18nFromProps", "t", "tFromProps", "additionalProps", "bind", "k", "reactI18nextOptions", "options", "react", "namespaces", "defaultNS", "nodeAsString", "defaultValue", "transEmptyNodeValue", "hashTransKey", "interpolation", "defaultVariables", "interpolationOverride", "alwaysFormat", "prefix", "suffix", "generatedComponents", "useAsParent", "defaultTransParent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/react-i18next/dist/es/TransWithoutContext.js"], "sourcesContent": ["import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      warn(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      warn(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    warn(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(children) && Object.hasOwnProperty.call(children, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = cloneElement(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return createElement(Fragment, null, comp);\n  }\n  return createElement(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if (isObject(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  warnOnce(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  const content = renderNodes(generatedComponents || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACvF,OAAOC,IAAI,MAAM,sBAAsB;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,YAAY;AAC/D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,WAAW,KAAK;EACzC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB,MAAME,IAAI,GAAGF,IAAI,CAACG,KAAK,EAAEC,QAAQ,IAAIJ,IAAI,CAACI,QAAQ;EAClD,IAAIH,WAAW,EAAE,OAAOC,IAAI,CAACG,MAAM,GAAG,CAAC;EACvC,OAAO,CAAC,CAACH,IAAI;AACf,CAAC;AACD,MAAMI,WAAW,GAAGN,IAAI,IAAI;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMI,QAAQ,GAAGJ,IAAI,CAACG,KAAK,EAAEC,QAAQ,IAAIJ,IAAI,CAACI,QAAQ;EACtD,OAAOJ,IAAI,CAACG,KAAK,EAAEI,iBAAiB,GAAGC,UAAU,CAACJ,QAAQ,CAAC,GAAGA,QAAQ;AACxE,CAAC;AACD,MAAMK,qBAAqB,GAAGL,QAAQ,IAAIM,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,IAAIA,QAAQ,CAACQ,KAAK,CAACxB,cAAc,CAAC;AACnG,MAAMoB,UAAU,GAAGK,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC9D,MAAMC,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACrC,MAAMC,SAAS,GAAG;IAChB,GAAGD;EACL,CAAC;EACDC,SAAS,CAACd,KAAK,GAAGe,MAAM,CAACC,MAAM,CAACJ,MAAM,CAACZ,KAAK,EAAEa,MAAM,CAACb,KAAK,CAAC;EAC3D,OAAOc,SAAS;AAClB,CAAC;AACD,OAAO,MAAMG,aAAa,GAAGA,CAAChB,QAAQ,EAAEiB,WAAW,EAAEC,IAAI,EAAEC,OAAO,KAAK;EACrE,IAAI,CAACnB,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIoB,UAAU,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAGjB,UAAU,CAACJ,QAAQ,CAAC;EAC1C,MAAMsB,SAAS,GAAGL,WAAW,EAAEM,0BAA0B,GAAGN,WAAW,CAACO,0BAA0B,IAAI,EAAE,GAAG,EAAE;EAC7GH,aAAa,CAACI,OAAO,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC3C,IAAIrC,QAAQ,CAACoC,KAAK,CAAC,EAAE;MACnBN,UAAU,IAAI,GAAGM,KAAK,EAAE;MACxB;IACF;IACA,IAAI1C,cAAc,CAAC0C,KAAK,CAAC,EAAE;MACzB,MAAM;QACJ3B,KAAK;QACL6B;MACF,CAAC,GAAGF,KAAK;MACT,MAAMG,eAAe,GAAGf,MAAM,CAACgB,IAAI,CAAC/B,KAAK,CAAC,CAACE,MAAM;MACjD,MAAM8B,eAAe,GAAGT,SAAS,CAACU,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC;MACpD,MAAMK,aAAa,GAAGlC,KAAK,CAACC,QAAQ;MACpC,IAAI,CAACiC,aAAa,IAAIF,eAAe,IAAI,CAACF,eAAe,EAAE;QACzDT,UAAU,IAAI,IAAIQ,IAAI,IAAI;QAC1B;MACF;MACA,IAAI,CAACK,aAAa,KAAK,CAACF,eAAe,IAAIF,eAAe,CAAC,IAAI9B,KAAK,CAACI,iBAAiB,EAAE;QACtFiB,UAAU,IAAI,IAAIO,UAAU,MAAMA,UAAU,GAAG;QAC/C;MACF;MACA,IAAII,eAAe,IAAIF,eAAe,KAAK,CAAC,IAAIvC,QAAQ,CAAC2C,aAAa,CAAC,EAAE;QACvEb,UAAU,IAAI,IAAIQ,IAAI,IAAIK,aAAa,KAAKL,IAAI,GAAG;QACnD;MACF;MACA,MAAMM,OAAO,GAAGlB,aAAa,CAACiB,aAAa,EAAEhB,WAAW,EAAEC,IAAI,EAAEC,OAAO,CAAC;MACxEC,UAAU,IAAI,IAAIO,UAAU,IAAIO,OAAO,KAAKP,UAAU,GAAG;MACzD;IACF;IACA,IAAID,KAAK,KAAK,IAAI,EAAE;MAClBnC,IAAI,CAAC2B,IAAI,EAAE,kBAAkB,EAAE,iCAAiC,EAAE;QAChEC;MACF,CAAC,CAAC;MACF;IACF;IACA,IAAI9B,QAAQ,CAACqC,KAAK,CAAC,EAAE;MACnB,MAAM;QACJS,MAAM;QACN,GAAGC;MACL,CAAC,GAAGV,KAAK;MACT,MAAMI,IAAI,GAAGhB,MAAM,CAACgB,IAAI,CAACM,KAAK,CAAC;MAC/B,IAAIN,IAAI,CAAC7B,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMoC,KAAK,GAAGF,MAAM,GAAG,GAAGL,IAAI,CAAC,CAAC,CAAC,KAAKK,MAAM,EAAE,GAAGL,IAAI,CAAC,CAAC,CAAC;QACxDV,UAAU,IAAI,KAAKiB,KAAK,IAAI;QAC5B;MACF;MACA9C,IAAI,CAAC2B,IAAI,EAAE,mBAAmB,EAAE,wFAAwF,EAAE;QACxHC,OAAO;QACPO;MACF,CAAC,CAAC;MACF;IACF;IACAnC,IAAI,CAAC2B,IAAI,EAAE,mBAAmB,EAAE,wGAAwG,EAAE;MACxIC,OAAO;MACPO;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAON,UAAU;AACnB,CAAC;AACD,MAAMkB,WAAW,GAAGA,CAACtC,QAAQ,EAAEuC,YAAY,EAAErB,IAAI,EAAED,WAAW,EAAEuB,aAAa,EAAEC,cAAc,KAAK;EAChG,IAAIF,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE;EAClC,MAAMjB,SAAS,GAAGL,WAAW,CAACO,0BAA0B,IAAI,EAAE;EAC9D,MAAMkB,6BAA6B,GAAGH,YAAY,IAAI,IAAII,MAAM,CAACrB,SAAS,CAACsB,GAAG,CAACC,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACR,YAAY,CAAC;EAChI,IAAI,CAACvC,QAAQ,IAAI,CAAC0C,6BAA6B,IAAI,CAACD,cAAc,EAAE,OAAO,CAACF,YAAY,CAAC;EACzF,MAAM9B,IAAI,GAAG,CAAC,CAAC;EACf,MAAMuC,OAAO,GAAGC,MAAM,IAAI;IACxB,MAAM5B,aAAa,GAAGjB,UAAU,CAAC6C,MAAM,CAAC;IACxC5B,aAAa,CAACI,OAAO,CAACC,KAAK,IAAI;MAC7B,IAAIpC,QAAQ,CAACoC,KAAK,CAAC,EAAE;MACrB,IAAI/B,WAAW,CAAC+B,KAAK,CAAC,EAAEsB,OAAO,CAAC9C,WAAW,CAACwB,KAAK,CAAC,CAAC,CAAC,KAAK,IAAIrC,QAAQ,CAACqC,KAAK,CAAC,IAAI,CAAC1C,cAAc,CAAC0C,KAAK,CAAC,EAAEZ,MAAM,CAACC,MAAM,CAACN,IAAI,EAAEiB,KAAK,CAAC;IACpI,CAAC,CAAC;EACJ,CAAC;EACDsB,OAAO,CAAChD,QAAQ,CAAC;EACjB,MAAMkD,GAAG,GAAG9D,IAAI,CAAC+D,KAAK,CAAC,MAAMZ,YAAY,MAAM,CAAC;EAChD,MAAMa,IAAI,GAAG;IACX,GAAG3C,IAAI;IACP,GAAG+B;EACL,CAAC;EACD,MAAMa,WAAW,GAAGA,CAAC3B,KAAK,EAAE9B,IAAI,EAAE0D,aAAa,KAAK;IAClD,MAAML,MAAM,GAAG/C,WAAW,CAACwB,KAAK,CAAC;IACjC,MAAM6B,cAAc,GAAGC,MAAM,CAACP,MAAM,EAAErD,IAAI,CAACI,QAAQ,EAAEsD,aAAa,CAAC;IACnE,OAAOjD,qBAAqB,CAAC4C,MAAM,CAAC,IAAIM,cAAc,CAACtD,MAAM,KAAK,CAAC,IAAIyB,KAAK,CAAC3B,KAAK,EAAEI,iBAAiB,GAAG8C,MAAM,GAAGM,cAAc;EACjI,CAAC;EACD,MAAME,iBAAiB,GAAGA,CAAC/B,KAAK,EAAEgC,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEC,MAAM,KAAK;IAC1D,IAAInC,KAAK,CAACoC,KAAK,EAAE;MACfpC,KAAK,CAAC1B,QAAQ,GAAG0D,KAAK;MACtBC,GAAG,CAACI,IAAI,CAAC9E,YAAY,CAACyC,KAAK,EAAE;QAC3BsC,GAAG,EAAEJ;MACP,CAAC,EAAEC,MAAM,GAAGI,SAAS,GAAGP,KAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLC,GAAG,CAACI,IAAI,CAAC,GAAG5E,QAAQ,CAACyD,GAAG,CAAC,CAAClB,KAAK,CAAC,EAAEwC,CAAC,IAAI;QACrC,MAAMnE,KAAK,GAAG;UACZ,GAAGmE,CAAC,CAACnE;QACP,CAAC;QACD,OAAOA,KAAK,CAACI,iBAAiB;QAC9B,OAAOjB,aAAa,CAACgF,CAAC,CAACtC,IAAI,EAAE;UAC3B,GAAG7B,KAAK;UACRiE,GAAG,EAAEJ,CAAC;UACNO,GAAG,EAAED,CAAC,CAACnE,KAAK,CAACoE,GAAG,IAAID,CAAC,CAACC;QACxB,CAAC,EAAEN,MAAM,GAAG,IAAI,GAAGH,KAAK,CAAC;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD,MAAMF,MAAM,GAAGA,CAACY,SAAS,EAAEC,OAAO,EAAEf,aAAa,KAAK;IACpD,MAAMgB,UAAU,GAAGlE,UAAU,CAACgE,SAAS,CAAC;IACxC,MAAMG,QAAQ,GAAGnE,UAAU,CAACiE,OAAO,CAAC;IACpC,OAAOE,QAAQ,CAACC,MAAM,CAAC,CAACb,GAAG,EAAE/D,IAAI,EAAEgE,CAAC,KAAK;MACvC,MAAMa,kBAAkB,GAAG7E,IAAI,CAACI,QAAQ,GAAG,CAAC,CAAC,EAAEkC,OAAO,IAAIhB,IAAI,CAACwD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAChF,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACkC,OAAO,EAAEkB,IAAI,EAAElC,IAAI,CAAC2D,QAAQ,CAAC;MAC/I,IAAIjF,IAAI,CAACgC,IAAI,KAAK,KAAK,EAAE;QACvB,IAAIkD,GAAG,GAAGR,UAAU,CAACS,QAAQ,CAACnF,IAAI,CAACoF,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI1B,aAAa,CAACrD,MAAM,KAAK,CAAC,IAAI,CAAC6E,GAAG,EAAEA,GAAG,GAAGxB,aAAa,CAAC,CAAC,CAAC,CAAC1D,IAAI,CAACoF,IAAI,CAAC;QACzE,IAAI,CAACF,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAClB,MAAMpD,KAAK,GAAGZ,MAAM,CAACgB,IAAI,CAAClC,IAAI,CAACqF,KAAK,CAAC,CAAChF,MAAM,KAAK,CAAC,GAAGS,UAAU,CAAC;UAC9DX,KAAK,EAAEH,IAAI,CAACqF;QACd,CAAC,EAAEH,GAAG,CAAC,GAAGA,GAAG;QACb,MAAMI,SAAS,GAAGlG,cAAc,CAAC0C,KAAK,CAAC;QACvC,MAAMyD,8BAA8B,GAAGD,SAAS,IAAIvF,WAAW,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAACwF,WAAW;QAChG,MAAMC,oBAAoB,GAAG3C,6BAA6B,IAAIrD,QAAQ,CAACqC,KAAK,CAAC,IAAIA,KAAK,CAACoC,KAAK,IAAI,CAACoB,SAAS;QAC1G,MAAMI,gBAAgB,GAAGjG,QAAQ,CAACW,QAAQ,CAAC,IAAIc,MAAM,CAACyE,cAAc,CAACC,IAAI,CAACxF,QAAQ,EAAEJ,IAAI,CAACoF,IAAI,CAAC;QAC9F,IAAI1F,QAAQ,CAACoC,KAAK,CAAC,EAAE;UACnB,MAAMW,KAAK,GAAGnB,IAAI,CAACwD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAClD,KAAK,EAAE0B,IAAI,EAAElC,IAAI,CAAC2D,QAAQ,CAAC;UAChFlB,GAAG,CAACI,IAAI,CAAC1B,KAAK,CAAC;QACjB,CAAC,MAAM,IAAI1C,WAAW,CAAC+B,KAAK,CAAC,IAAIyD,8BAA8B,EAAE;UAC/D,MAAMzB,KAAK,GAAGL,WAAW,CAAC3B,KAAK,EAAE9B,IAAI,EAAE0D,aAAa,CAAC;UACrDG,iBAAiB,CAAC/B,KAAK,EAAEgC,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAIyB,oBAAoB,EAAE;UAC/B,MAAM3B,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE1E,IAAI,CAACI,QAAQ,EAAEsD,aAAa,CAAC;UAC9DG,iBAAiB,CAAC/B,KAAK,EAAEgC,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI6B,MAAM,CAACC,KAAK,CAACC,UAAU,CAAC/F,IAAI,CAACoF,IAAI,CAAC,CAAC,EAAE;UAC9C,IAAIM,gBAAgB,EAAE;YACpB,MAAM5B,KAAK,GAAGL,WAAW,CAAC3B,KAAK,EAAE9B,IAAI,EAAE0D,aAAa,CAAC;YACrDG,iBAAiB,CAAC/B,KAAK,EAAEgC,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEhE,IAAI,CAACwF,WAAW,CAAC;UAC3D,CAAC,MAAM,IAAInE,WAAW,CAACM,0BAA0B,IAAID,SAAS,CAACU,OAAO,CAACpC,IAAI,CAACoF,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACtF,IAAIpF,IAAI,CAACwF,WAAW,EAAE;cACpBzB,GAAG,CAACI,IAAI,CAAC7E,aAAa,CAACU,IAAI,CAACoF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGpE,IAAI,CAACoF,IAAI,IAAIpB,CAAC;cACxB,CAAC,CAAC,CAAC;YACL,CAAC,MAAM;cACL,MAAMF,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE1E,IAAI,CAACI,QAAQ,EAAEsD,aAAa,CAAC;cAC9DK,GAAG,CAACI,IAAI,CAAC7E,aAAa,CAACU,IAAI,CAACoF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGpE,IAAI,CAACoF,IAAI,IAAIpB,CAAC;cACxB,CAAC,EAAEF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,MAAM,IAAI9D,IAAI,CAACwF,WAAW,EAAE;YAC3BzB,GAAG,CAACI,IAAI,CAAC,IAAInE,IAAI,CAACoF,IAAI,KAAK,CAAC;UAC9B,CAAC,MAAM;YACL,MAAMtB,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE1E,IAAI,CAACI,QAAQ,EAAEsD,aAAa,CAAC;YAC9DK,GAAG,CAACI,IAAI,CAAC,IAAInE,IAAI,CAACoF,IAAI,IAAItB,KAAK,KAAK9D,IAAI,CAACoF,IAAI,GAAG,CAAC;UACnD;QACF,CAAC,MAAM,IAAI3F,QAAQ,CAACqC,KAAK,CAAC,IAAI,CAACwD,SAAS,EAAE;UACxC,MAAMhD,OAAO,GAAGtC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGyE,kBAAkB,GAAG,IAAI;UAC5D,IAAIvC,OAAO,EAAEyB,GAAG,CAACI,IAAI,CAAC7B,OAAO,CAAC;QAChC,CAAC,MAAM;UACLuB,iBAAiB,CAAC/B,KAAK,EAAE+C,kBAAkB,EAAEd,GAAG,EAAEC,CAAC,EAAEhE,IAAI,CAACI,QAAQ,CAACC,MAAM,KAAK,CAAC,IAAI,CAACwE,kBAAkB,CAAC;QACzG;MACF,CAAC,MAAM,IAAI7E,IAAI,CAACgC,IAAI,KAAK,MAAM,EAAE;QAC/B,MAAMgE,aAAa,GAAG3E,WAAW,CAAC4E,kBAAkB;QACpD,MAAM3D,OAAO,GAAGO,cAAc,GAAGxB,WAAW,CAAC6E,QAAQ,CAAC5E,IAAI,CAACwD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAChF,IAAI,CAACsC,OAAO,EAAEkB,IAAI,EAAElC,IAAI,CAAC2D,QAAQ,CAAC,CAAC,GAAG3D,IAAI,CAACwD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAChF,IAAI,CAACsC,OAAO,EAAEkB,IAAI,EAAElC,IAAI,CAAC2D,QAAQ,CAAC;QAC5M,IAAIe,aAAa,EAAE;UACjBjC,GAAG,CAACI,IAAI,CAAC7E,aAAa,CAAC0G,aAAa,EAAE;YACpC5B,GAAG,EAAE,GAAGpE,IAAI,CAACoF,IAAI,IAAIpB,CAAC;UACxB,CAAC,EAAE1B,OAAO,CAAC,CAAC;QACd,CAAC,MAAM;UACLyB,GAAG,CAACI,IAAI,CAAC7B,OAAO,CAAC;QACnB;MACF;MACA,OAAOyB,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,MAAMoC,MAAM,GAAGvC,MAAM,CAAC,CAAC;IACrBM,KAAK,EAAE,IAAI;IACX9D,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC,CAAC,EAAEkD,GAAG,EAAE9C,UAAU,CAACJ,QAAQ,IAAI,EAAE,CAAC,CAAC;EACpC,OAAOE,WAAW,CAAC6F,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AACD,MAAMC,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,WAAW,KAAK;EAC3D,MAAMC,YAAY,GAAGH,SAAS,CAACjC,GAAG,IAAIkC,KAAK;EAC3C,MAAMG,IAAI,GAAGpH,YAAY,CAACgH,SAAS,EAAE;IACnCjC,GAAG,EAAEoC;EACP,CAAC,CAAC;EACF,IAAI,CAACC,IAAI,CAACtG,KAAK,IAAI,CAACsG,IAAI,CAACtG,KAAK,CAACC,QAAQ,IAAImG,WAAW,CAACnE,OAAO,CAAC,GAAGkE,KAAK,IAAI,CAAC,GAAG,CAAC,IAAIC,WAAW,CAACnE,OAAO,CAAC,GAAGkE,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE;IAC1H,OAAOG,IAAI;EACb;EACA,SAASC,aAAaA,CAAA,EAAG;IACvB,OAAOpH,aAAa,CAACH,QAAQ,EAAE,IAAI,EAAEsH,IAAI,CAAC;EAC5C;EACA,OAAOnH,aAAa,CAACoH,aAAa,EAAE;IAClCtC,GAAG,EAAEoC;EACP,CAAC,CAAC;AACJ,CAAC;AACD,MAAMG,uBAAuB,GAAGA,CAACC,UAAU,EAAEL,WAAW,KAAKK,UAAU,CAAC5D,GAAG,CAAC,CAACsB,CAAC,EAAEgC,KAAK,KAAKF,iBAAiB,CAAC9B,CAAC,EAAEgC,KAAK,EAAEC,WAAW,CAAC,CAAC;AACnI,MAAMM,wBAAwB,GAAGA,CAACD,UAAU,EAAEL,WAAW,KAAK;EAC5D,MAAMO,YAAY,GAAG,CAAC,CAAC;EACvB5F,MAAM,CAACgB,IAAI,CAAC0E,UAAU,CAAC,CAAC/E,OAAO,CAACyC,CAAC,IAAI;IACnCpD,MAAM,CAACC,MAAM,CAAC2F,YAAY,EAAE;MAC1B,CAACxC,CAAC,GAAG8B,iBAAiB,CAACQ,UAAU,CAACtC,CAAC,CAAC,EAAEA,CAAC,EAAEiC,WAAW;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOO,YAAY;AACrB,CAAC;AACD,MAAMC,kBAAkB,GAAGA,CAACH,UAAU,EAAEL,WAAW,EAAEjF,IAAI,EAAEC,OAAO,KAAK;EACrE,IAAI,CAACqF,UAAU,EAAE,OAAO,IAAI;EAC5B,IAAIlG,KAAK,CAACC,OAAO,CAACiG,UAAU,CAAC,EAAE;IAC7B,OAAOD,uBAAuB,CAACC,UAAU,EAAEL,WAAW,CAAC;EACzD;EACA,IAAI9G,QAAQ,CAACmH,UAAU,CAAC,EAAE;IACxB,OAAOC,wBAAwB,CAACD,UAAU,EAAEL,WAAW,CAAC;EAC1D;EACA3G,QAAQ,CAAC0B,IAAI,EAAE,0BAA0B,EAAE,wDAAwD,EAAE;IACnGC;EACF,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;AACD,OAAO,SAASyF,KAAKA,CAAC;EACpB5G,QAAQ;EACR6G,KAAK;EACLC,MAAM;EACN3F,OAAO;EACP4F,OAAO;EACPC,QAAQ,GAAG,CAAC,CAAC;EACbC,MAAM;EACNC,QAAQ;EACRV,UAAU;EACVW,EAAE;EACFjG,IAAI,EAAEkG,aAAa;EACnBC,CAAC,EAAEC,UAAU;EACb7E,cAAc;EACd,GAAG8E;AACL,CAAC,EAAE;EACD,MAAMrG,IAAI,GAAGkG,aAAa,IAAI1H,OAAO,CAAC,CAAC;EACvC,IAAI,CAACwB,IAAI,EAAE;IACT1B,QAAQ,CAAC0B,IAAI,EAAE,qBAAqB,EAAE,yEAAyE,EAAE;MAC/GC;IACF,CAAC,CAAC;IACF,OAAOnB,QAAQ;EACjB;EACA,MAAMqH,CAAC,GAAGC,UAAU,IAAIpG,IAAI,CAACmG,CAAC,CAACG,IAAI,CAACtG,IAAI,CAAC,KAAKuG,CAAC,IAAIA,CAAC,CAAC;EACrD,MAAMC,mBAAmB,GAAG;IAC1B,GAAGjI,WAAW,CAAC,CAAC;IAChB,GAAGyB,IAAI,CAACyG,OAAO,EAAEC;EACnB,CAAC;EACD,IAAIC,UAAU,GAAGV,EAAE,IAAIE,CAAC,CAACF,EAAE,IAAIjG,IAAI,CAACyG,OAAO,EAAEG,SAAS;EACtDD,UAAU,GAAGvI,QAAQ,CAACuI,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAChF,MAAME,YAAY,GAAG/G,aAAa,CAAChB,QAAQ,EAAE0H,mBAAmB,EAAExG,IAAI,EAAEC,OAAO,CAAC;EAChF,MAAM6G,YAAY,GAAGd,QAAQ,IAAIa,YAAY,IAAIL,mBAAmB,CAACO,mBAAmB,IAAI9G,OAAO;EACnG,MAAM;IACJ+G;EACF,CAAC,GAAGR,mBAAmB;EACvB,MAAM1D,GAAG,GAAG7C,OAAO,KAAK+G,YAAY,GAAGA,YAAY,CAACH,YAAY,IAAIC,YAAY,CAAC,GAAGD,YAAY,IAAIC,YAAY,CAAC;EACjH,IAAI9G,IAAI,CAACyG,OAAO,EAAEQ,aAAa,EAAEC,gBAAgB,EAAE;IACjDnB,MAAM,GAAGA,MAAM,IAAInG,MAAM,CAACgB,IAAI,CAACmF,MAAM,CAAC,CAAChH,MAAM,GAAG,CAAC,GAAG;MAClD,GAAGgH,MAAM;MACT,GAAG/F,IAAI,CAACyG,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC,GAAG;MACF,GAAGlH,IAAI,CAACyG,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC;EACH;EACA,MAAMC,qBAAqB,GAAGpB,MAAM,IAAIJ,KAAK,KAAK5C,SAAS,IAAI,CAAC/C,IAAI,CAACyG,OAAO,EAAEQ,aAAa,EAAEG,YAAY,IAAI,CAACtI,QAAQ,GAAGgH,QAAQ,CAACmB,aAAa,GAAG;IAChJA,aAAa,EAAE;MACb,GAAGnB,QAAQ,CAACmB,aAAa;MACzBI,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV;EACF,CAAC;EACD,MAAMhG,aAAa,GAAG;IACpB,GAAGwE,QAAQ;IACXD,OAAO,EAAEA,OAAO,IAAIC,QAAQ,CAACD,OAAO;IACpCF,KAAK;IACL,GAAGI,MAAM;IACT,GAAGoB,qBAAqB;IACxBL,YAAY;IACZb,EAAE,EAAEU;EACN,CAAC;EACD,MAAM1B,WAAW,GAAGnC,GAAG,GAAGqD,CAAC,CAACrD,GAAG,EAAExB,aAAa,CAAC,GAAGwF,YAAY;EAC9D,MAAMS,mBAAmB,GAAG9B,kBAAkB,CAACH,UAAU,EAAEL,WAAW,EAAEjF,IAAI,EAAEC,OAAO,CAAC;EACtF,MAAMe,OAAO,GAAGI,WAAW,CAACmG,mBAAmB,IAAIzI,QAAQ,EAAEmG,WAAW,EAAEjF,IAAI,EAAEwG,mBAAmB,EAAElF,aAAa,EAAEC,cAAc,CAAC;EACnI,MAAMiG,WAAW,GAAG5B,MAAM,IAAIY,mBAAmB,CAACiB,kBAAkB;EACpE,OAAOD,WAAW,GAAGxJ,aAAa,CAACwJ,WAAW,EAAEnB,eAAe,EAAErF,OAAO,CAAC,GAAGA,OAAO;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}