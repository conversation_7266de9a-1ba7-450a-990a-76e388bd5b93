Imports System
Imports System.Drawing
Imports System.Windows.Forms

Namespace LicenseActivation.Forms
    ''' <summary>
    ''' The main form of the application
    ''' </summary>
    Public Class MainForm
        Inherits Form
        Private ReadOnly _licenseManager As LicenseActivation.Classes.LicenseManager
        Private _isLicensed As Boolean = False

        ''' <summary>
        ''' Initializes a new instance of the MainForm class
        ''' </summary>
        Public Sub New()
            ' This call is required by the designer
            InitializeComponent()

            ' Initialize license manager
            _licenseManager = New LicenseActivation.Classes.LicenseManager()

            ' Set form properties
            Me.Text = "License Activation"
            ' Me.Icon = My.Resources.AppIcon ' Resource not available

            ' Add event handlers
            AddHandler btnActivate.Click, AddressOf BtnActivate_Click
            AddHandler btnViewLicense.Click, AddressOf BtnViewLicense_Click
            AddHandler btnDeactivate.Click, AddressOf BtnDeactivate_Click
            AddHandler btnExit.Click, AddressOf BtnExit_Click

            ' Check license status on load
            AddHandler Me.Load, AddressOf MainForm_Load
        End Sub

        ''' <summary>
        ''' Required designer variable
        ''' </summary>
        Private components As System.ComponentModel.IContainer = Nothing

        ''' <summary>
        ''' UI Controls
        ''' </summary>
        Private WithEvents pnlHeader As Panel
        Private WithEvents lblTitle As Label
        Private WithEvents pnlContent As Panel
        Private WithEvents lblLicenseKey As Label
        Private WithEvents txtLicenseKey As TextBox
        Private WithEvents btnActivate As Button
        Private WithEvents btnViewLicense As Button
        Private WithEvents btnDeactivate As Button
        Private WithEvents btnExit As Button
        Private WithEvents lblStatus As Label
        Private WithEvents pnlFooter As Panel
        Private WithEvents lblCopyright As Label
        Private WithEvents picLogo As PictureBox



        ''' <summary>
        ''' Required method for Designer support - do not modify
        ''' the contents of this method with the code editor
        ''' </summary>
        Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(600, 400)
            Me.StartPosition = FormStartPosition.CenterScreen
            Me.FormBorderStyle = FormBorderStyle.FixedSingle
            Me.MaximizeBox = False
            Me.MinimizeBox = True

            ' Create header panel
            pnlHeader = New Panel()
            pnlHeader.Dock = DockStyle.Top
            pnlHeader.Height = 60
            pnlHeader.BackColor = Color.FromArgb(59, 130, 246) ' Blue

            ' Create logo
            picLogo = New PictureBox()
            picLogo.Size = New Size(40, 40)
            picLogo.Location = New Point(10, 10)
            picLogo.SizeMode = PictureBoxSizeMode.StretchImage
            ' If My.Resources.AppLogo IsNot Nothing Then
            '     picLogo.Image = My.Resources.AppLogo
            ' End If

            ' Create title label
            lblTitle = New Label()
            lblTitle.Text = "License Activation Client"
            lblTitle.Font = New Font("Segoe UI", 16, FontStyle.Bold)
            lblTitle.ForeColor = Color.White
            lblTitle.AutoSize = True
            lblTitle.Location = New Point(60, 15)

            ' Create content panel
            pnlContent = New Panel()
            pnlContent.Dock = DockStyle.Fill
            pnlContent.Padding = New Padding(20)

            ' Create license key label
            lblLicenseKey = New Label()
            lblLicenseKey.Text = "License Key:"
            lblLicenseKey.Font = New Font("Segoe UI", 10)
            lblLicenseKey.AutoSize = True
            lblLicenseKey.Location = New Point(20, 30)

            ' Create license key textbox
            txtLicenseKey = New TextBox()
            txtLicenseKey.Width = 400
            txtLicenseKey.Height = 30
            txtLicenseKey.Font = New Font("Segoe UI", 10)
            txtLicenseKey.Location = New Point(20, 55)

            ' Create activate button
            btnActivate = New Button()
            btnActivate.Text = "Activate License"
            btnActivate.Width = 150
            btnActivate.Height = 40
            btnActivate.Font = New Font("Segoe UI", 10)
            btnActivate.BackColor = Color.FromArgb(59, 130, 246) ' Blue
            btnActivate.ForeColor = Color.White
            btnActivate.FlatStyle = FlatStyle.Flat
            btnActivate.Location = New Point(20, 100)

            ' Create view license button
            btnViewLicense = New Button()
            btnViewLicense.Text = "View License"
            btnViewLicense.Width = 150
            btnViewLicense.Height = 40
            btnViewLicense.Font = New Font("Segoe UI", 10)
            btnViewLicense.BackColor = Color.FromArgb(16, 185, 129) ' Green
            btnViewLicense.ForeColor = Color.White
            btnViewLicense.FlatStyle = FlatStyle.Flat
            btnViewLicense.Location = New Point(180, 100)
            btnViewLicense.Visible = False ' Initially hidden

            ' Create deactivate button
            btnDeactivate = New Button()
            btnDeactivate.Text = "Deactivate License"
            btnDeactivate.Width = 150
            btnDeactivate.Height = 40
            btnDeactivate.Font = New Font("Segoe UI", 10)
            btnDeactivate.BackColor = Color.FromArgb(239, 68, 68) ' Red
            btnDeactivate.ForeColor = Color.White
            btnDeactivate.FlatStyle = FlatStyle.Flat
            btnDeactivate.Location = New Point(340, 100)
            btnDeactivate.Visible = False ' Initially hidden

            ' Create exit button
            btnExit = New Button()
            btnExit.Text = "Exit"
            btnExit.Width = 100
            btnExit.Height = 40
            btnExit.Font = New Font("Segoe UI", 10)
            btnExit.BackColor = Color.FromArgb(107, 114, 128) ' Gray
            btnExit.ForeColor = Color.White
            btnExit.FlatStyle = FlatStyle.Flat
            btnExit.Location = New Point(480, 320)

            ' Create status label
            lblStatus = New Label()
            lblStatus.Text = "Please enter your license key to activate the application."
            lblStatus.Font = New Font("Segoe UI", 10)
            lblStatus.ForeColor = Color.FromArgb(107, 114, 128) ' Gray
            lblStatus.AutoSize = True
            lblStatus.Location = New Point(20, 160)

            ' Create footer panel
            pnlFooter = New Panel()
            pnlFooter.Dock = DockStyle.Bottom
            pnlFooter.Height = 30
            pnlFooter.BackColor = Color.FromArgb(243, 244, 246) ' Light Gray

            ' Create copyright label
            lblCopyright = New Label()
            lblCopyright.Text = "© " & DateTime.Now.Year.ToString() & " License Management System. All rights reserved."
            lblCopyright.Font = New Font("Segoe UI", 8)
            lblCopyright.ForeColor = Color.FromArgb(107, 114, 128) ' Gray
            lblCopyright.AutoSize = True
            lblCopyright.Location = New Point(10, 8)

            ' Add controls to header panel
            pnlHeader.Controls.Add(picLogo)
            pnlHeader.Controls.Add(lblTitle)

            ' Add controls to content panel
            pnlContent.Controls.Add(lblLicenseKey)
            pnlContent.Controls.Add(txtLicenseKey)
            pnlContent.Controls.Add(btnActivate)
            pnlContent.Controls.Add(btnViewLicense)
            pnlContent.Controls.Add(btnDeactivate)
            pnlContent.Controls.Add(btnExit)
            pnlContent.Controls.Add(lblStatus)

            ' Add controls to footer panel
            pnlFooter.Controls.Add(lblCopyright)

            ' Add panels to form
            Me.Controls.Add(pnlContent)
            Me.Controls.Add(pnlHeader)
            Me.Controls.Add(pnlFooter)
        End Sub

        ''' <summary>
        ''' Handles the form load event
        ''' </summary>
        Private Async Sub MainForm_Load(sender As Object, e As EventArgs)
            ' Check if the application is already licensed
            _isLicensed = _licenseManager.IsLicensed()

            If _isLicensed Then
                ' Validate the license with the server
                _isLicensed = Await _licenseManager.ValidateLicenseAsync()

                If _isLicensed Then
                    ' Update UI for licensed state
                    UpdateUIForLicensedState()
                Else
                    ' Update UI for unlicensed state
                    UpdateUIForUnlicensedState()
                End If
            Else
                ' Update UI for unlicensed state
                UpdateUIForUnlicensedState()
            End If
        End Sub

        ''' <summary>
        ''' Updates the UI for the licensed state
        ''' </summary>
        Private Sub UpdateUIForLicensedState()
            ' Get current license
            Dim license As LicenseActivation.Classes.License = _licenseManager.GetCurrentLicense()

            ' Update UI
            txtLicenseKey.Text = license.LicenseKey
            txtLicenseKey.Enabled = False
            btnActivate.Visible = False
            btnViewLicense.Visible = True
            btnDeactivate.Visible = True

            ' Update status label
            If license.IsExpiringSoon() Then
                lblStatus.Text = "Your license is active but will expire in " & license.GetDaysRemaining().ToString() & " days. License type: " & license.LicenseType
                lblStatus.ForeColor = Color.FromArgb(245, 158, 11) ' Amber
            Else
                lblStatus.Text = "Your license is active and will expire on " & LicenseActivation.Modules.Utilities.FormatDate(license.ExpiryDate) & ". License type: " & license.LicenseType
                lblStatus.ForeColor = Color.FromArgb(16, 185, 129) ' Green
            End If
        End Sub

        ''' <summary>
        ''' Updates the UI for the unlicensed state
        ''' </summary>
        Private Sub UpdateUIForUnlicensedState()
            ' Update UI
            txtLicenseKey.Text = ""
            txtLicenseKey.Enabled = True
            btnActivate.Visible = True
            btnViewLicense.Visible = False
            btnDeactivate.Visible = False

            ' Update status label
            lblStatus.Text = "Please enter your license key to activate the application."
            lblStatus.ForeColor = Color.FromArgb(107, 114, 128) ' Gray
        End Sub

        ''' <summary>
        ''' Handles the activate button click event
        ''' </summary>
        Private Async Sub BtnActivate_Click(sender As Object, e As EventArgs)
            ' Validate license key
            If String.IsNullOrWhiteSpace(txtLicenseKey.Text) Then
                MessageBox.Show("Please enter a valid license key.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' Disable UI during activation
            txtLicenseKey.Enabled = False
            btnActivate.Enabled = False
            lblStatus.Text = "Activating license..."
            lblStatus.ForeColor = Color.FromArgb(107, 114, 128) ' Gray

            ' Activate license
            Dim success As Boolean = Await _licenseManager.ActivateLicenseAsync(txtLicenseKey.Text)

            If success Then
                ' Update UI for licensed state
                _isLicensed = True
                UpdateUIForLicensedState()

                ' Show success message
                MessageBox.Show("License activated successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' Open license view form
                Dim licenseViewForm As New LicenseActivation.Forms.LicenseViewForm(_licenseManager.GetCurrentLicense())
                licenseViewForm.ShowDialog()
            Else
                ' Update UI for unlicensed state
                _isLicensed = False
                UpdateUIForUnlicensedState()
            End If
        End Sub

        ''' <summary>
        ''' Handles the view license button click event
        ''' </summary>
        Private Sub BtnViewLicense_Click(sender As Object, e As EventArgs)
            ' Open license view form
            Dim licenseViewForm As New LicenseActivation.Forms.LicenseViewForm(_licenseManager.GetCurrentLicense())
            licenseViewForm.ShowDialog()
        End Sub

        ''' <summary>
        ''' Handles the deactivate button click event
        ''' </summary>
        Private Async Sub BtnDeactivate_Click(sender As Object, e As EventArgs)
            ' Confirm deactivation
            Dim result As DialogResult = MessageBox.Show("Are you sure you want to deactivate your license? This will remove the license from this computer.", "Confirm Deactivation", MessageBoxButtons.YesNo, MessageBoxIcon.Warning)

            If result = DialogResult.Yes Then
                ' Disable UI during deactivation
                btnDeactivate.Enabled = False
                lblStatus.Text = "Deactivating license..."
                lblStatus.ForeColor = Color.FromArgb(107, 114, 128) ' Gray

                ' Deactivate license
                Dim success As Boolean = Await _licenseManager.DeactivateLicenseAsync()

                If success Then
                    ' Update UI for unlicensed state
                    _isLicensed = False
                    UpdateUIForUnlicensedState()

                    ' Show success message
                    MessageBox.Show("License deactivated successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    ' Re-enable UI
                    btnDeactivate.Enabled = True
                End If
            End If
        End Sub

        ''' <summary>
        ''' Handles the exit button click event
        ''' </summary>
        Private Sub BtnExit_Click(sender As Object, e As EventArgs)
            ' Close the application
            Application.Exit()
        End Sub
    End Class
End Namespace