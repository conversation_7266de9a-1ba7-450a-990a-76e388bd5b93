{"ast": null, "code": "const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\nexport { Browser as default };", "map": {"version": 3, "names": ["slice", "for<PERSON>ach", "defaults", "obj", "call", "arguments", "source", "prop", "undefined", "hasXSS", "input", "xssPatterns", "some", "pattern", "test", "fieldContentRegExp", "serializeCookie", "name", "val", "options", "length", "path", "opt", "value", "encodeURIComponent", "str", "maxAge", "Number", "isNaN", "Error", "Math", "floor", "domain", "TypeError", "expires", "toUTCString", "httpOnly", "secure", "sameSite", "toLowerCase", "partitioned", "cookie", "create", "minutes", "cookieOptions", "Date", "setTime", "getTime", "document", "read", "nameEQ", "ca", "split", "i", "c", "char<PERSON>t", "substring", "indexOf", "remove", "cookie$1", "lookup", "_ref", "lookup<PERSON><PERSON><PERSON>", "cacheUserLanguage", "lng", "_ref2", "cookieMinutes", "cookieDomain", "querystring", "lookupQuerystring", "found", "window", "search", "location", "hash", "query", "params", "pos", "key", "hasLocalStorageSupport", "localStorageAvailable", "localStorage", "<PERSON><PERSON><PERSON>", "setItem", "removeItem", "e", "lookupLocalStorage", "getItem", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "navigator", "languages", "userLanguage", "language", "push", "htmlTag", "internalHtmlTag", "documentElement", "getAttribute", "lookupFromPathIndex", "pathname", "match", "Array", "isArray", "index", "replace", "subdomain", "lookupFromSubdomainIndex", "internalLookupFromSubdomainIndex", "hostname", "canCookies", "order", "splice", "getDefaults", "caches", "excludeCache<PERSON>or", "convertDetectedLanguage", "l", "Browser", "constructor", "services", "type", "detectors", "init", "languageUtils", "i18nOptions", "lookupFromUrlIndex", "addDetector", "detector", "detect", "detectionOrder", "detected", "detectorName", "concat", "filter", "d", "map", "getBestMatchFromCodes", "cacheName", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "mappings": "AAAA,MAAM;EACJA,KAAK;EACLC;AACF,CAAC,GAAG,EAAE;AACN,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrBF,OAAO,CAACG,IAAI,CAACJ,KAAK,CAACI,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC,EAAEC,MAAM,IAAI;IAC/C,IAAIA,MAAM,EAAE;MACV,KAAK,MAAMC,IAAI,IAAID,MAAM,EAAE;QACzB,IAAIH,GAAG,CAACI,IAAI,CAAC,KAAKC,SAAS,EAAEL,GAAG,CAACI,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ;AACA,SAASM,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK;;EAE3C;EACA,MAAMC,WAAW,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,YAAY,CAAC;EACzR,OAAOA,WAAW,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC;AACzD;;AAEA;AACA,MAAMK,kBAAkB,GAAG,uCAAuC;AAClE,MAAMC,eAAe,GAAG,SAAAA,CAAUC,IAAI,EAAEC,GAAG,EAAE;EAC3C,IAAIC,OAAO,GAAGd,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG;IAChFgB,IAAI,EAAE;EACR,CAAC;EACD,MAAMC,GAAG,GAAGH,OAAO;EACnB,MAAMI,KAAK,GAAGC,kBAAkB,CAACN,GAAG,CAAC;EACrC,IAAIO,GAAG,GAAG,GAAGR,IAAI,IAAIM,KAAK,EAAE;EAC5B,IAAID,GAAG,CAACI,MAAM,GAAG,CAAC,EAAE;IAClB,MAAMA,MAAM,GAAGJ,GAAG,CAACI,MAAM,GAAG,CAAC;IAC7B,IAAIC,MAAM,CAACC,KAAK,CAACF,MAAM,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,2BAA2B,CAAC;IACtEJ,GAAG,IAAI,aAAaK,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC,EAAE;EAC1C;EACA,IAAIJ,GAAG,CAACU,MAAM,EAAE;IACd,IAAI,CAACjB,kBAAkB,CAACD,IAAI,CAACQ,GAAG,CAACU,MAAM,CAAC,EAAE;MACxC,MAAM,IAAIC,SAAS,CAAC,0BAA0B,CAAC;IACjD;IACAR,GAAG,IAAI,YAAYH,GAAG,CAACU,MAAM,EAAE;EACjC;EACA,IAAIV,GAAG,CAACD,IAAI,EAAE;IACZ,IAAI,CAACN,kBAAkB,CAACD,IAAI,CAACQ,GAAG,CAACD,IAAI,CAAC,EAAE;MACtC,MAAM,IAAIY,SAAS,CAAC,wBAAwB,CAAC;IAC/C;IACAR,GAAG,IAAI,UAAUH,GAAG,CAACD,IAAI,EAAE;EAC7B;EACA,IAAIC,GAAG,CAACY,OAAO,EAAE;IACf,IAAI,OAAOZ,GAAG,CAACY,OAAO,CAACC,WAAW,KAAK,UAAU,EAAE;MACjD,MAAM,IAAIF,SAAS,CAAC,2BAA2B,CAAC;IAClD;IACAR,GAAG,IAAI,aAAaH,GAAG,CAACY,OAAO,CAACC,WAAW,CAAC,CAAC,EAAE;EACjD;EACA,IAAIb,GAAG,CAACc,QAAQ,EAAEX,GAAG,IAAI,YAAY;EACrC,IAAIH,GAAG,CAACe,MAAM,EAAEZ,GAAG,IAAI,UAAU;EACjC,IAAIH,GAAG,CAACgB,QAAQ,EAAE;IAChB,MAAMA,QAAQ,GAAG,OAAOhB,GAAG,CAACgB,QAAQ,KAAK,QAAQ,GAAGhB,GAAG,CAACgB,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAGjB,GAAG,CAACgB,QAAQ;IAC7F,QAAQA,QAAQ;MACd,KAAK,IAAI;QACPb,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,KAAK;QACRA,GAAG,IAAI,gBAAgB;QACvB;MACF,KAAK,QAAQ;QACXA,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,MAAM;QACTA,GAAG,IAAI,iBAAiB;QACxB;MACF;QACE,MAAM,IAAIQ,SAAS,CAAC,4BAA4B,CAAC;IACrD;EACF;EACA,IAAIX,GAAG,CAACkB,WAAW,EAAEf,GAAG,IAAI,eAAe;EAC3C,OAAOA,GAAG;AACZ,CAAC;AACD,MAAMgB,MAAM,GAAG;EACbC,MAAMA,CAACzB,IAAI,EAAEM,KAAK,EAAEoB,OAAO,EAAEX,MAAM,EAAE;IACnC,IAAIY,aAAa,GAAGvC,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG;MACtFgB,IAAI,EAAE,GAAG;MACTiB,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIK,OAAO,EAAE;MACXC,aAAa,CAACV,OAAO,GAAG,IAAIW,IAAI,CAAC,CAAC;MAClCD,aAAa,CAACV,OAAO,CAACY,OAAO,CAACF,aAAa,CAACV,OAAO,CAACa,OAAO,CAAC,CAAC,GAAGJ,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;IACtF;IACA,IAAIX,MAAM,EAAEY,aAAa,CAACZ,MAAM,GAAGA,MAAM;IACzCgB,QAAQ,CAACP,MAAM,GAAGzB,eAAe,CAACC,IAAI,EAAEO,kBAAkB,CAACD,KAAK,CAAC,EAAEqB,aAAa,CAAC;EACnF,CAAC;EACDK,IAAIA,CAAChC,IAAI,EAAE;IACT,MAAMiC,MAAM,GAAG,GAAGjC,IAAI,GAAG;IACzB,MAAMkC,EAAE,GAAGH,QAAQ,CAACP,MAAM,CAACW,KAAK,CAAC,GAAG,CAAC;IACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,EAAE,CAAC/B,MAAM,EAAEiC,CAAC,EAAE,EAAE;MAClC,IAAIC,CAAC,GAAGH,EAAE,CAACE,CAAC,CAAC;MACb,OAAOC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAED,CAAC,GAAGA,CAAC,CAACE,SAAS,CAAC,CAAC,EAAEF,CAAC,CAAClC,MAAM,CAAC;MACxD,IAAIkC,CAAC,CAACG,OAAO,CAACP,MAAM,CAAC,KAAK,CAAC,EAAE,OAAOI,CAAC,CAACE,SAAS,CAACN,MAAM,CAAC9B,MAAM,EAAEkC,CAAC,CAAClC,MAAM,CAAC;IAC1E;IACA,OAAO,IAAI;EACb,CAAC;EACDsC,MAAMA,CAACzC,IAAI,EAAE;IACX,IAAI,CAACyB,MAAM,CAACzB,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EAC3B;AACF,CAAC;AACD,IAAI0C,QAAQ,GAAG;EACb1C,IAAI,EAAE,QAAQ;EACd;EACA2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACFC;IACF,CAAC,GAAGD,IAAI;IACR,IAAIC,YAAY,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;MACnD,OAAOP,MAAM,CAACQ,IAAI,CAACa,YAAY,CAAC,IAAItD,SAAS;IAC/C;IACA,OAAOA,SAAS;EAClB,CAAC;EACD;EACAuD,iBAAiBA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC5B,IAAI;MACFH,YAAY;MACZI,aAAa;MACbC,YAAY;MACZvB;IACF,CAAC,GAAGqB,KAAK;IACT,IAAIH,YAAY,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;MACnDP,MAAM,CAACC,MAAM,CAACoB,YAAY,EAAEE,GAAG,EAAEE,aAAa,EAAEC,YAAY,EAAEvB,aAAa,CAAC;IAC9E;EACF;AACF,CAAC;AAED,IAAIwB,WAAW,GAAG;EAChBnD,IAAI,EAAE,aAAa;EACnB;EACA2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACFQ;IACF,CAAC,GAAGR,IAAI;IACR,IAAIS,KAAK;IACT,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MACjC,IAAI;QACFC;MACF,CAAC,GAAGD,MAAM,CAACE,QAAQ;MACnB,IAAI,CAACF,MAAM,CAACE,QAAQ,CAACD,MAAM,IAAID,MAAM,CAACE,QAAQ,CAACC,IAAI,EAAEjB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACtEe,MAAM,GAAGD,MAAM,CAACE,QAAQ,CAACC,IAAI,CAAClB,SAAS,CAACe,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACjB,OAAO,CAAC,GAAG,CAAC,CAAC;MAC5E;MACA,MAAMkB,KAAK,GAAGH,MAAM,CAAChB,SAAS,CAAC,CAAC,CAAC;MACjC,MAAMoB,MAAM,GAAGD,KAAK,CAACvB,KAAK,CAAC,GAAG,CAAC;MAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,MAAM,CAACxD,MAAM,EAAEiC,CAAC,EAAE,EAAE;QACtC,MAAMwB,GAAG,GAAGD,MAAM,CAACvB,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,CAAC;QAClC,IAAIoB,GAAG,GAAG,CAAC,EAAE;UACX,MAAMC,GAAG,GAAGF,MAAM,CAACvB,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC,EAAEqB,GAAG,CAAC;UACvC,IAAIC,GAAG,KAAKT,iBAAiB,EAAE;YAC7BC,KAAK,GAAGM,MAAM,CAACvB,CAAC,CAAC,CAACG,SAAS,CAACqB,GAAG,GAAG,CAAC,CAAC;UACtC;QACF;MACF;IACF;IACA,OAAOP,KAAK;EACd;AACF,CAAC;AAED,IAAIS,sBAAsB,GAAG,IAAI;AACjC,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAClC,IAAID,sBAAsB,KAAK,IAAI,EAAE,OAAOA,sBAAsB;EAClE,IAAI;IACFA,sBAAsB,GAAG,OAAOR,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACU,YAAY,KAAK,IAAI;IACtF,IAAI,CAACF,sBAAsB,EAAE;MAC3B,OAAO,KAAK;IACd;IACA,MAAMG,OAAO,GAAG,uBAAuB;IACvCX,MAAM,CAACU,YAAY,CAACE,OAAO,CAACD,OAAO,EAAE,KAAK,CAAC;IAC3CX,MAAM,CAACU,YAAY,CAACG,UAAU,CAACF,OAAO,CAAC;EACzC,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVN,sBAAsB,GAAG,KAAK;EAChC;EACA,OAAOA,sBAAsB;AAC/B,CAAC;AACD,IAAIE,YAAY,GAAG;EACjBhE,IAAI,EAAE,cAAc;EACpB;EACA2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACFyB;IACF,CAAC,GAAGzB,IAAI;IACR,IAAIyB,kBAAkB,IAAIN,qBAAqB,CAAC,CAAC,EAAE;MACjD,OAAOT,MAAM,CAACU,YAAY,CAACM,OAAO,CAACD,kBAAkB,CAAC,IAAI9E,SAAS,CAAC,CAAC;IACvE;IACA,OAAOA,SAAS;EAClB,CAAC;EACD;EACAuD,iBAAiBA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC5B,IAAI;MACFqB;IACF,CAAC,GAAGrB,KAAK;IACT,IAAIqB,kBAAkB,IAAIN,qBAAqB,CAAC,CAAC,EAAE;MACjDT,MAAM,CAACU,YAAY,CAACE,OAAO,CAACG,kBAAkB,EAAEtB,GAAG,CAAC;IACtD;EACF;AACF,CAAC;AAED,IAAIwB,wBAAwB,GAAG,IAAI;AACnC,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC,IAAID,wBAAwB,KAAK,IAAI,EAAE,OAAOA,wBAAwB;EACtE,IAAI;IACFA,wBAAwB,GAAG,OAAOjB,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACmB,cAAc,KAAK,IAAI;IAC1F,IAAI,CAACF,wBAAwB,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,MAAMN,OAAO,GAAG,uBAAuB;IACvCX,MAAM,CAACmB,cAAc,CAACP,OAAO,CAACD,OAAO,EAAE,KAAK,CAAC;IAC7CX,MAAM,CAACmB,cAAc,CAACN,UAAU,CAACF,OAAO,CAAC;EAC3C,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVG,wBAAwB,GAAG,KAAK;EAClC;EACA,OAAOA,wBAAwB;AACjC,CAAC;AACD,IAAIE,cAAc,GAAG;EACnBzE,IAAI,EAAE,gBAAgB;EACtB2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACF8B;IACF,CAAC,GAAG9B,IAAI;IACR,IAAI8B,oBAAoB,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MACrD,OAAOlB,MAAM,CAACmB,cAAc,CAACH,OAAO,CAACI,oBAAoB,CAAC,IAAInF,SAAS;IACzE;IACA,OAAOA,SAAS;EAClB,CAAC;EACDuD,iBAAiBA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC5B,IAAI;MACF0B;IACF,CAAC,GAAG1B,KAAK;IACT,IAAI0B,oBAAoB,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MACrDlB,MAAM,CAACmB,cAAc,CAACP,OAAO,CAACQ,oBAAoB,EAAE3B,GAAG,CAAC;IAC1D;EACF;AACF,CAAC;AAED,IAAI4B,WAAW,GAAG;EAChB3E,IAAI,EAAE,WAAW;EACjB2C,MAAMA,CAACzC,OAAO,EAAE;IACd,MAAMmD,KAAK,GAAG,EAAE;IAChB,IAAI,OAAOuB,SAAS,KAAK,WAAW,EAAE;MACpC,MAAM;QACJC,SAAS;QACTC,YAAY;QACZC;MACF,CAAC,GAAGH,SAAS;MACb,IAAIC,SAAS,EAAE;QACb;QACA,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,SAAS,CAAC1E,MAAM,EAAEiC,CAAC,EAAE,EAAE;UACzCiB,KAAK,CAAC2B,IAAI,CAACH,SAAS,CAACzC,CAAC,CAAC,CAAC;QAC1B;MACF;MACA,IAAI0C,YAAY,EAAE;QAChBzB,KAAK,CAAC2B,IAAI,CAACF,YAAY,CAAC;MAC1B;MACA,IAAIC,QAAQ,EAAE;QACZ1B,KAAK,CAAC2B,IAAI,CAACD,QAAQ,CAAC;MACtB;IACF;IACA,OAAO1B,KAAK,CAAClD,MAAM,GAAG,CAAC,GAAGkD,KAAK,GAAG9D,SAAS;EAC7C;AACF,CAAC;AAED,IAAI0F,OAAO,GAAG;EACZjF,IAAI,EAAE,SAAS;EACf;EACA2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACFqC;IACF,CAAC,GAAGrC,IAAI;IACR,IAAIS,KAAK;IACT,MAAM6B,eAAe,GAAGD,OAAO,KAAK,OAAOlD,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACoD,eAAe,GAAG,IAAI,CAAC;IACtG,IAAID,eAAe,IAAI,OAAOA,eAAe,CAACE,YAAY,KAAK,UAAU,EAAE;MACzE/B,KAAK,GAAG6B,eAAe,CAACE,YAAY,CAAC,MAAM,CAAC;IAC9C;IACA,OAAO/B,KAAK;EACd;AACF,CAAC;AAED,IAAIjD,IAAI,GAAG;EACTJ,IAAI,EAAE,MAAM;EACZ;EACA2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACFyC;IACF,CAAC,GAAGzC,IAAI;IACR,IAAI,OAAOU,MAAM,KAAK,WAAW,EAAE,OAAO/D,SAAS;IACnD,MAAMwF,QAAQ,GAAGzB,MAAM,CAACE,QAAQ,CAAC8B,QAAQ,CAACC,KAAK,CAAC,iBAAiB,CAAC;IAClE,IAAI,CAACC,KAAK,CAACC,OAAO,CAACV,QAAQ,CAAC,EAAE,OAAOxF,SAAS;IAC9C,MAAMmG,KAAK,GAAG,OAAOL,mBAAmB,KAAK,QAAQ,GAAGA,mBAAmB,GAAG,CAAC;IAC/E,OAAON,QAAQ,CAACW,KAAK,CAAC,EAAEC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAC1C;AACF,CAAC;AAED,IAAIC,SAAS,GAAG;EACd5F,IAAI,EAAE,WAAW;EACjB2C,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI;MACFiD;IACF,CAAC,GAAGjD,IAAI;IACR;IACA,MAAMkD,gCAAgC,GAAG,OAAOD,wBAAwB,KAAK,QAAQ,GAAGA,wBAAwB,GAAG,CAAC,GAAG,CAAC;IACxH;IACA;IACA;IACA,MAAMd,QAAQ,GAAG,OAAOzB,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACE,QAAQ,EAAEuC,QAAQ,EAAER,KAAK,CAAC,wDAAwD,CAAC;;IAE5I;IACA,IAAI,CAACR,QAAQ,EAAE,OAAOxF,SAAS;IAC/B;IACA,OAAOwF,QAAQ,CAACe,gCAAgC,CAAC;EACnD;AACF,CAAC;;AAED;AACA,IAAIE,UAAU,GAAG,KAAK;AACtB,IAAI;EACF;EACAjE,QAAQ,CAACP,MAAM;EACfwE,UAAU,GAAG,IAAI;EACjB;AACF,CAAC,CAAC,OAAO5B,CAAC,EAAE,CAAC;AACb,MAAM6B,KAAK,GAAG,CAAC,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,CAAC;AACjG,IAAI,CAACD,UAAU,EAAEC,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACnC,MAAMC,WAAW,GAAGA,CAAA,MAAO;EACzBF,KAAK;EACL7C,iBAAiB,EAAE,KAAK;EACxBP,YAAY,EAAE,SAAS;EACvBwB,kBAAkB,EAAE,YAAY;EAChCK,oBAAoB,EAAE,YAAY;EAClC;EACA0B,MAAM,EAAE,CAAC,cAAc,CAAC;EACxBC,eAAe,EAAE,CAAC,QAAQ,CAAC;EAC3B;EACA;;EAEAC,uBAAuB,EAAEC,CAAC,IAAIA;AAChC,CAAC,CAAC;AACF,MAAMC,OAAO,CAAC;EACZC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAIxG,OAAO,GAAGd,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACuH,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,IAAI,CAACH,QAAQ,EAAExG,OAAO,CAAC;EAC9B;EACA2G,IAAIA,CAAA,EAAG;IACL,IAAIH,QAAQ,GAAGtH,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG;MACjF0H,aAAa,EAAE,CAAC;IAClB,CAAC;IACD,IAAI5G,OAAO,GAAGd,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI2H,WAAW,GAAG3H,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACxF,IAAI,CAACsH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACxG,OAAO,GAAGjB,QAAQ,CAACiB,OAAO,EAAE,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAEiG,WAAW,CAAC,CAAC,CAAC;IACnE,IAAI,OAAO,IAAI,CAACjG,OAAO,CAACoG,uBAAuB,KAAK,QAAQ,IAAI,IAAI,CAACpG,OAAO,CAACoG,uBAAuB,CAAC9D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1H,IAAI,CAACtC,OAAO,CAACoG,uBAAuB,GAAGC,CAAC,IAAIA,CAAC,CAACZ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACjE;;IAEA;IACA,IAAI,IAAI,CAACzF,OAAO,CAAC8G,kBAAkB,EAAE,IAAI,CAAC9G,OAAO,CAACmF,mBAAmB,GAAG,IAAI,CAACnF,OAAO,CAAC8G,kBAAkB;IACvG,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,WAAW,CAACvE,QAAQ,CAAC;IAC1B,IAAI,CAACuE,WAAW,CAAC9D,WAAW,CAAC;IAC7B,IAAI,CAAC8D,WAAW,CAACjD,YAAY,CAAC;IAC9B,IAAI,CAACiD,WAAW,CAACxC,cAAc,CAAC;IAChC,IAAI,CAACwC,WAAW,CAACtC,WAAW,CAAC;IAC7B,IAAI,CAACsC,WAAW,CAAChC,OAAO,CAAC;IACzB,IAAI,CAACgC,WAAW,CAAC7G,IAAI,CAAC;IACtB,IAAI,CAAC6G,WAAW,CAACrB,SAAS,CAAC;EAC7B;EACAqB,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACN,SAAS,CAACM,QAAQ,CAAClH,IAAI,CAAC,GAAGkH,QAAQ;IACxC,OAAO,IAAI;EACb;EACAC,MAAMA,CAAA,EAAG;IACP,IAAIC,cAAc,GAAGhI,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACc,OAAO,CAAC+F,KAAK;IAC3G,IAAIoB,QAAQ,GAAG,EAAE;IACjBD,cAAc,CAACpI,OAAO,CAACsI,YAAY,IAAI;MACrC,IAAI,IAAI,CAACV,SAAS,CAACU,YAAY,CAAC,EAAE;QAChC,IAAI3E,MAAM,GAAG,IAAI,CAACiE,SAAS,CAACU,YAAY,CAAC,CAAC3E,MAAM,CAAC,IAAI,CAACzC,OAAO,CAAC;QAC9D,IAAIyC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAEA,MAAM,GAAG,CAACA,MAAM,CAAC;QAC3D,IAAIA,MAAM,EAAE0E,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAAC5E,MAAM,CAAC;MAChD;IACF,CAAC,CAAC;IACF0E,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKlI,SAAS,IAAIkI,CAAC,KAAK,IAAI,IAAI,CAACjI,MAAM,CAACiI,CAAC,CAAC,CAAC,CAACC,GAAG,CAACD,CAAC,IAAI,IAAI,CAACvH,OAAO,CAACoG,uBAAuB,CAACmB,CAAC,CAAC,CAAC;IAC9H,IAAI,IAAI,CAACf,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACI,aAAa,IAAI,IAAI,CAACJ,QAAQ,CAACI,aAAa,CAACa,qBAAqB,EAAE,OAAON,QAAQ,CAAC,CAAC;IACxH,OAAOA,QAAQ,CAAClH,MAAM,GAAG,CAAC,GAAGkH,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EACnD;EACAvE,iBAAiBA,CAACC,GAAG,EAAE;IACrB,IAAIqD,MAAM,GAAGhH,SAAS,CAACe,MAAM,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACc,OAAO,CAACkG,MAAM;IACpG,IAAI,CAACA,MAAM,EAAE;IACb,IAAI,IAAI,CAAClG,OAAO,CAACmG,eAAe,IAAI,IAAI,CAACnG,OAAO,CAACmG,eAAe,CAAC7D,OAAO,CAACO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IACpFqD,MAAM,CAACpH,OAAO,CAAC4I,SAAS,IAAI;MAC1B,IAAI,IAAI,CAAChB,SAAS,CAACgB,SAAS,CAAC,EAAE,IAAI,CAAChB,SAAS,CAACgB,SAAS,CAAC,CAAC9E,iBAAiB,CAACC,GAAG,EAAE,IAAI,CAAC7C,OAAO,CAAC;IAC/F,CAAC,CAAC;EACJ;AACF;AACAsG,OAAO,CAACG,IAAI,GAAG,kBAAkB;AAEjC,SAASH,OAAO,IAAIqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}