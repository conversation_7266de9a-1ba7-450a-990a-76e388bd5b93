/**
 * Alert routes for the License Verification API
 * Handles security alerts and notifications
 */

const express = require('express');
const router = express.Router();
const alertController = require('../controllers/alert.controller');
const authMiddleware = require('../middleware/auth.middleware');

/**
 * @route POST /api/alert/security
 * @desc Send security alert (tampering, debugging, etc.)
 * @access Public (but requires valid license key in request)
 */
router.post('/security', alertController.sendSecurityAlert);

/**
 * @route POST /api/alert/telegram
 * @desc Send alert to Telegram
 * @access Public (but requires valid license key in request)
 */
router.post('/telegram', alertController.sendTelegramAlert);

/**
 * @route GET /api/alert/history
 * @desc Get alert history
 * @access Protected (Admin only)
 */
router.get('/history', authMiddleware.verifyToken, authMiddleware.isAdmin, alertController.getAlertHistory);

/**
 * @route GET /api/alert/stats
 * @desc Get alert statistics
 * @access Protected (Admin only)
 */
router.get('/stats', authMiddleware.verifyToken, authMiddleware.isAdmin, alertController.getAlertStats);

/**
 * @route DELETE /api/alert/:id
 * @desc Delete an alert
 * @access Protected (Admin only)
 */
router.delete('/:id', authMiddleware.verifyToken, authMiddleware.isAdmin, alertController.deleteAlert);

module.exports = router;