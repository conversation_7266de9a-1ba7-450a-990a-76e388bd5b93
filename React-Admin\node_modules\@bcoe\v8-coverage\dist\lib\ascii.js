"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const compare_1 = require("./compare");
function emitForest(trees) {
    return emitForestLines(trees).join("\n");
}
exports.emitForest = emitForest;
function emitForestLines(trees) {
    const colMap = getColMap(trees);
    const header = emitOffsets(colMap);
    return [header, ...trees.map(tree => emitTree(tree, colMap).join("\n"))];
}
exports.emitForestLines = emitForestLines;
function getColMap(trees) {
    const eventSet = new Set();
    for (const tree of trees) {
        const stack = [tree];
        while (stack.length > 0) {
            const cur = stack.pop();
            eventSet.add(cur.start);
            eventSet.add(cur.end);
            for (const child of cur.children) {
                stack.push(child);
            }
        }
    }
    const events = [...eventSet];
    events.sort((a, b) => a - b);
    let maxDigits = 1;
    for (const event of events) {
        maxDigits = Math.max(maxDigits, event.toString(10).length);
    }
    const colWidth = maxDigits + 3;
    const colMap = new Map();
    for (const [i, event] of events.entries()) {
        colMap.set(event, i * colWidth);
    }
    return colMap;
}
function emitTree(tree, colMap) {
    const layers = [];
    let nextLayer = [tree];
    while (nextLayer.length > 0) {
        const layer = nextLayer;
        layers.push(layer);
        nextLayer = [];
        for (const node of layer) {
            for (const child of node.children) {
                nextLayer.push(child);
            }
        }
    }
    return layers.map(layer => emitTreeLayer(layer, colMap));
}
function parseFunctionRanges(text, offsetMap) {
    const result = [];
    for (const line of text.split("\n")) {
        for (const range of parseTreeLayer(line, offsetMap)) {
            result.push(range);
        }
    }
    result.sort(compare_1.compareRangeCovs);
    return result;
}
exports.parseFunctionRanges = parseFunctionRanges;
/**
 *
 * @param layer Sorted list of disjoint trees.
 * @param colMap
 */
function emitTreeLayer(layer, colMap) {
    const line = [];
    let curIdx = 0;
    for (const { start, end, count } of layer) {
        const startIdx = colMap.get(start);
        const endIdx = colMap.get(end);
        if (startIdx > curIdx) {
            line.push(" ".repeat(startIdx - curIdx));
        }
        line.push(emitRange(count, endIdx - startIdx));
        curIdx = endIdx;
    }
    return line.join("");
}
function parseTreeLayer(text, offsetMap) {
    const result = [];
    const regex = /\[(\d+)-*\)/gs;
    while (true) {
        const match = regex.exec(text);
        if (match === null) {
            break;
        }
        const startIdx = match.index;
        const endIdx = startIdx + match[0].length;
        const count = parseInt(match[1], 10);
        const startOffset = offsetMap.get(startIdx);
        const endOffset = offsetMap.get(endIdx);
        if (startOffset === undefined || endOffset === undefined) {
            throw new Error(`Invalid offsets for: ${JSON.stringify(text)}`);
        }
        result.push({ startOffset, endOffset, count });
    }
    return result;
}
function emitRange(count, len) {
    const rangeStart = `[${count.toString(10)}`;
    const rangeEnd = ")";
    const hyphensLen = len - (rangeStart.length + rangeEnd.length);
    const hyphens = "-".repeat(Math.max(0, hyphensLen));
    return `${rangeStart}${hyphens}${rangeEnd}`;
}
function emitOffsets(colMap) {
    let line = "";
    for (const [event, col] of colMap) {
        if (line.length < col) {
            line += " ".repeat(col - line.length);
        }
        line += event.toString(10);
    }
    return line;
}
function parseOffsets(text) {
    const result = new Map();
    const regex = /\d+/gs;
    while (true) {
        const match = regex.exec(text);
        if (match === null) {
            break;
        }
        result.set(match.index, parseInt(match[0], 10));
    }
    return result;
}
exports.parseOffsets = parseOffsets;

//# sourceMappingURL=data:application/json;charset=utf8;base64,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
