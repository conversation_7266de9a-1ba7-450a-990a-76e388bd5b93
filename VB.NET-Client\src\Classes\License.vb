Imports System
Imports System.Collections.Generic
Imports Newtonsoft.Json

Namespace LicenseActivation.Classes
    ''' <summary>
    ''' Represents a license for the application
    ''' </summary>
    Public Class License
        ''' <summary>
        ''' Gets or sets the license ID
        ''' </summary>
        <JsonProperty("id")>
        Public Property Id As String

        ''' <summary>
        ''' Gets or sets the license key
        ''' </summary>
        <JsonProperty("licenseKey")>
        Public Property LicenseKey As String

        ''' <summary>
        ''' Gets or sets the client ID associated with this license
        ''' </summary>
        <JsonProperty("clientId")>
        Public Property ClientId As String

        ''' <summary>
        ''' Gets or sets the client name
        ''' </summary>
        <JsonProperty("clientName")>
        Public Property ClientName As String

        ''' <summary>
        ''' Gets or sets the client email
        ''' </summary>
        <JsonProperty("clientEmail")>
        Public Property ClientEmail As String

        ''' <summary>
        ''' Gets or sets the customer name (alias for ClientName)
        ''' </summary>
        Public Property CustomerName As String
            Get
                Return ClientName
            End Get
            Set(value As String)
                ClientName = value
            End Set
        End Property

        ''' <summary>
        ''' Gets or sets the customer email (alias for ClientEmail)
        ''' </summary>
        Public Property CustomerEmail As String
            Get
                Return ClientEmail
            End Get
            Set(value As String)
                ClientEmail = value
            End Set
        End Property

        ''' <summary>
        ''' Gets or sets the client's Telegram ID
        ''' </summary>
        <JsonProperty("telegramId")>
        Public Property TelegramId As String

        ''' <summary>
        ''' Gets or sets the license type (Standard, Premium, Enterprise)
        ''' </summary>
        <JsonProperty("licenseType")>
        Public Property LicenseType As String

        ''' <summary>
        ''' Gets or sets the activation date of the license
        ''' </summary>
        <JsonProperty("activationDate")>
        Public Property ActivationDate As DateTime

        ''' <summary>
        ''' Gets or sets the issue date of the license (alias for ActivationDate)
        ''' </summary>
        Public Property IssueDate As DateTime
            Get
                Return ActivationDate
            End Get
            Set(value As DateTime)
                ActivationDate = value
            End Set
        End Property

        ''' <summary>
        ''' Gets or sets the expiry date of the license
        ''' </summary>
        <JsonProperty("expiryDate")>
        Public Property ExpiryDate As DateTime

        ''' <summary>
        ''' Gets or sets whether the license is active
        ''' </summary>
        <JsonProperty("isActive")>
        Public Property IsActive As Boolean

        ''' <summary>
        ''' Gets or sets the permissions associated with this license
        ''' </summary>
        <JsonProperty("permissions")>
        Public Property Permissions As List(Of String)

        ''' <summary>
        ''' Gets or sets the features associated with this license (alias for Permissions)
        ''' </summary>
        Public Property Features As List(Of String)
            Get
                Return Permissions
            End Get
            Set(value As List(Of String))
                Permissions = value
            End Set
        End Property

        ''' <summary>
        ''' Gets or sets any notes associated with this license
        ''' </summary>
        <JsonProperty("notes")>
        Public Property Notes As String

        ''' <summary>
        ''' Gets or sets the hardware ID associated with this license activation
        ''' </summary>
        <JsonProperty("hardwareId")>
        Public Property HardwareId As String

        ''' <summary>
        ''' Gets or sets the last check date when the license was validated
        ''' </summary>
        <JsonProperty("lastCheckDate")>
        Public Property LastCheckDate As DateTime

        ''' <summary>
        ''' Checks if the license is expired
        ''' </summary>
        ''' <returns>True if the license is expired, otherwise false</returns>
        Public Function IsExpired() As Boolean
            Return DateTime.Now > ExpiryDate
        End Function

        ''' <summary>
        ''' Checks if the license is expiring soon (within 30 days)
        ''' </summary>
        ''' <returns>True if the license is expiring soon, otherwise false</returns>
        Public Function IsExpiringSoon() As Boolean
            Dim daysRemaining As Integer = (ExpiryDate - DateTime.Now).Days
            Return daysRemaining <= 30 AndAlso daysRemaining > 0
        End Function

        ''' <summary>
        ''' Gets the number of days remaining until the license expires
        ''' </summary>
        ''' <returns>The number of days remaining</returns>
        Public Function GetDaysRemaining() As Integer
            Dim daysRemaining As Integer = (ExpiryDate - DateTime.Now).Days
            Return If(daysRemaining < 0, 0, daysRemaining)
        End Function

        ''' <summary>
        ''' Checks if the license has a specific permission
        ''' </summary>
        ''' <param name="permission">The permission to check</param>
        ''' <returns>True if the license has the permission, otherwise false</returns>
        Public Function HasPermission(permission As String) As Boolean
            Return Permissions IsNot Nothing AndAlso Permissions.Contains(permission)
        End Function
    End Class
End Namespace