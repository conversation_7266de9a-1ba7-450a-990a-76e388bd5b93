/**
 * Authentication routes for the License Verification API
 * Handles user login, registration, and token management
 */

const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const authMiddleware = require('../middleware/auth.middleware');

/**
 * @route POST /api/auth/register
 * @desc Register a new admin user
 * @access Protected (Super Admin only)
 */
router.post('/register', authMiddleware.verifyToken, authMiddleware.isSuperAdmin, authController.register);

/**
 * @route POST /api/auth/login
 * @desc Login and get authentication token
 * @access Public
 */
router.post('/login', authController.login);

/**
 * @route POST /api/auth/refresh
 * @desc Refresh authentication token
 * @access Public (with refresh token)
 */
router.post('/refresh', authController.refreshToken);

/**
 * @route POST /api/auth/logout
 * @desc Logout and invalidate token
 * @access Protected
 */
router.post('/logout', authMiddleware.verifyToken, authController.logout);

/**
 * @route GET /api/auth/profile
 * @desc Get current user profile
 * @access Protected
 */
router.get('/profile', authMiddleware.verifyToken, authController.getProfile);

/**
 * @route PUT /api/auth/profile
 * @desc Update current user profile
 * @access Protected
 */
router.put('/profile', authMiddleware.verifyToken, authController.updateProfile);

/**
 * @route PUT /api/auth/change-password
 * @desc Change current user password
 * @access Protected
 */
router.put('/change-password', authMiddleware.verifyToken, authController.changePassword);

module.exports = router;