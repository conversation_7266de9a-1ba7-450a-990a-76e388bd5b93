!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).i18nextHttpBackend=e()}(function(){return function o(r,i,s){function a(t,e){if(!i[t]){if(!r[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(u)return u(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}n=i[t]={exports:{}},r[t][0].call(n.exports,function(e){return a(r[t][1][e]||e)},n,n.exports,o,r,i,s)}return i[t].exports}for(var u="function"==typeof require&&require,e=0;e<s.length;e++)a(s[e]);return a}({1:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=e("./utils.js"),r=(e=e("./request.js"))&&e.__esModule?e:{default:e};function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(t,e){var n,o=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,n)),o}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach(function(e){c(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function u(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,f(o.key),o)}}function c(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){e=((e,t)=>{if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if("object"!=i(n=n.call(e,t||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string");return"symbol"==i(e)?e:e+""}e=function e(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=this,i=e;if(!(r instanceof i))throw new TypeError("Cannot call a class as a function");this.services=t,this.options=n,this.allOptions=o,this.type="backend",this.init(t,n,o)},(d=[{key:"init",value:function(e){var t=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.services=e,this.options=s(s(s({},{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(e){return JSON.parse(e)},stringify:JSON.stringify,parsePayload:function(e,t,n){return c({},t,n||"")},parseLoadPayload:function(e,t){},request:r.default,reloadInterval:"undefined"==typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}),this.options||{}),n),this.allOptions=o,this.services&&this.options.reloadInterval&&"object"===i(e=setInterval(function(){return t.reload()},this.options.reloadInterval))&&"function"==typeof e.unref&&e.unref()}},{key:"readMulti",value:function(e,t,n){this._readAny(e,e,t,t,n)}},{key:"read",value:function(e,t,n){this._readAny([e],e,[t],t,n)}},{key:"_readAny",value:function(t,n,o,r,i){var s=this,e=this.options.loadPath;"function"==typeof this.options.loadPath&&(e=this.options.loadPath(t,o)),(e=(0,a.makePromise)(e)).then(function(e){if(!e)return i(null,{});e=s.services.interpolator.interpolate(e,{lng:t.join("+"),ns:o.join("+")});s.loadUrl(e,i,n,r)})}},{key:"loadUrl",value:function(i,s,a,u){var c=this,e=this.options.parseLoadPayload("string"==typeof a?[a]:a,"string"==typeof u?[u]:u);this.options.request(this.options,i,e,function(e,t){if(t&&(500<=t.status&&t.status<600||!t.status))return s("failed loading "+i+"; status code: "+t.status,!0);if(t&&400<=t.status&&t.status<500)return s("failed loading "+i+"; status code: "+t.status,!1);if(!t&&e&&e.message){var n=e.message.toLowerCase();if(["failed","fetch","network","load"].find(function(e){return-1<n.indexOf(e)}))return s("failed loading "+i+": "+e.message,!0)}if(e)return s(e,!1);var o,r;try{o="string"==typeof t.data?c.options.parse(t.data,a,u):t.data}catch(e){r="failed parsing "+i+" to json"}if(r)return s(r,!1);s(null,o)})}},{key:"create",value:function(n,o,e,t,r){var i,s,a,u,c=this;this.options.addPath&&("string"==typeof n&&(n=[n]),i=this.options.parsePayload(o,e,t),s=0,a=[],u=[],n.forEach(function(e){var t=c.options.addPath,t=("function"==typeof c.options.addPath&&(t=c.options.addPath(e,o)),c.services.interpolator.interpolate(t,{lng:e,ns:o}));c.options.request(c.options,t,i,function(e,t){s+=1,a.push(e),u.push(t),s===n.length&&"function"==typeof r&&r(a,u)})}))}},{key:"reload",value:function(){var t,e,n=this,o=this.services,r=o.backendConnector,i=o.languageUtils,s=o.logger,o=r.language;o&&"cimode"===o.toLowerCase()||(t=[],(e=function(e){i.toResolveHierarchy(e).forEach(function(e){t.indexOf(e)<0&&t.push(e)})})(o),this.allOptions.preload&&this.allOptions.preload.forEach(e),t.forEach(function(o){n.allOptions.ns.forEach(function(n){r.read(o,n,"read",null,null,function(e,t){e&&s.warn("loading namespace ".concat(n," for language ").concat(o," failed"),e),!e&&t&&s.log("loaded namespace ".concat(n," for language ").concat(o),t),r.loaded("".concat(o,"|").concat(n),e,t)})})}))}}])&&u(e.prototype,d),l&&u(e,l),Object.defineProperty(e,"prototype",{writable:!1});var l,d=e;d.type="backend",n.default=d;t.exports=n.default},{"./request.js":2,"./utils.js":3}],2:[function(e,n,o){!function(S){!function(){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var h=e("./utils.js");function t(t,e){var n,o=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,n)),o}function b(o){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t(Object(r),!0).forEach(function(e){var t,n;t=o,n=r[e=e],(e=(e=>(e=((e,t)=>{if("object"!=v(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if("object"!=v(n=n.call(e,t||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string"),"symbol"==v(e)?e:e+""))(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(r,e))})}return o}function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m,g,O="function"==typeof fetch?fetch:void 0;if(void 0!==S&&S.fetch?O=S.fetch:"undefined"!=typeof window&&window.fetch&&(O=window.fetch),(0,h.hasXMLHttpRequest)()&&(void 0!==S&&S.XMLHttpRequest?m=S.XMLHttpRequest:"undefined"!=typeof window&&window.XMLHttpRequest&&(m=window.XMLHttpRequest)),"function"==typeof ActiveXObject&&(void 0!==S&&S.ActiveXObject?g=S.ActiveXObject:"undefined"!=typeof window&&window.ActiveXObject&&(g=window.ActiveXObject)),!(O="function"!=typeof O?void 0:O)&&!m&&!g)try{O=e("cross-fetch")}catch(e){}var w=function(e,t){if(t&&"object"===v(t)){var n,o="";for(n in t)o+="&"+encodeURIComponent(n)+"="+encodeURIComponent(t[n]);if(!o)return e;e=e+(-1!==e.indexOf("?")?"&":"?")+o.slice(1)}return e},j=function(e,t,n,o){function r(t){if(!t.ok)return n(t.statusText||"Error",{status:t.status});t.text().then(function(e){n(null,{status:t.status,data:e})}).catch(n)}if(o){o=o(e,t);if(o instanceof Promise)return void o.then(r).catch(n)}("function"==typeof fetch?fetch:O)(e,t).then(r).catch(n)},P=!1;o.default=function(e,t,n,o){if("function"==typeof n&&(o=n,n=void 0),o=o||function(){},O&&0!==t.indexOf("file:")){var r=e,i=t,s=n,a=o,u=(r.queryStringParams&&(i=w(i,r.queryStringParams)),b({},"function"==typeof r.customHeaders?r.customHeaders():r.customHeaders)),c=("undefined"==typeof window&&void 0!==S&&void 0!==S.process&&S.process.versions&&S.process.versions.node&&(u["User-Agent"]="i18next-http-backend (node/".concat(S.process.version,"; ").concat(S.process.platform," ").concat(S.process.arch,")")),s&&(u["Content-Type"]="application/json"),"function"==typeof r.requestOptions?r.requestOptions(s):r.requestOptions),f=b({method:s?"POST":"GET",body:s?r.stringify(s):void 0,headers:u},P?{}:c),s="function"==typeof r.alternateFetch&&1<=r.alternateFetch.length?r.alternateFetch:void 0;try{j(i,f,a,s)}catch(e){if(!c||0===Object.keys(c).length||!e.message||e.message.indexOf("not implemented")<0)return a(e);try{Object.keys(c).forEach(function(e){delete f[e]}),j(i,f,a,s),P=!0}catch(e){a(e)}}}else if((0,h.hasXMLHttpRequest)()||"function"==typeof ActiveXObject){var u=e,r=t,c=n,l=o;c&&"object"===v(c)&&(c=w("",c).slice(1)),u.queryStringParams&&(r=w(r,u.queryStringParams));try{var d=m?new m:new g("MSXML2.XMLHTTP.3.0"),p=(d.open(c?"POST":"GET",r,1),u.crossDomain||d.setRequestHeader("X-Requested-With","XMLHttpRequest"),d.withCredentials=!!u.withCredentials,c&&d.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),d.overrideMimeType&&d.overrideMimeType("application/json"),u.customHeaders);if(p="function"==typeof p?p():p)for(var y in p)d.setRequestHeader(y,p[y]);d.onreadystatechange=function(){3<d.readyState&&l(400<=d.status?d.statusText:null,{status:d.status,data:d.responseText})},d.send(c)}catch(e){console&&console.log(e)}}else o(new Error("No fetch and no xhr implementation found!"))};n.exports=o.default}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./utils.js":3,"cross-fetch":4}],3:[function(e,t,n){function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.defaults=function(n){return r.call(i.call(arguments,1),function(e){if(e)for(var t in e)void 0===n[t]&&(n[t]=e[t])}),n},n.hasXMLHttpRequest=function(){return"function"==typeof XMLHttpRequest||"object"===("undefined"==typeof XMLHttpRequest?"undefined":o(XMLHttpRequest))},n.makePromise=function(e){if((e=>e&&"function"==typeof e.then)(e))return e;return Promise.resolve(e)};var n=[],r=n.forEach,i=n.slice},{}],4:[function(e,t,n){},{}]},{},[1])(1)});