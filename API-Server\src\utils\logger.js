/**
 * Logger utility for the License Verification API
 * Uses Winston for structured logging
 */

const winston = require('winston');

/**
 * Setup logging configuration
 * @returns {winston.Logger} Configured logger instance
 */
const setupLogging = () => {
  // Define log format
  const logFormat = winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  );

  // Create logger instance
  const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: { service: 'license-verification-api' },
    transports: [
      // Write logs to console
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      }),
      // Write logs to file
      new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
      new winston.transports.File({ filename: 'logs/combined.log' })
    ]
  });

  // Create logs directory if it doesn't exist
  const fs = require('fs');
  const path = require('path');
  const logsDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir);
  }

  return logger;
};

/**
 * Log security events specifically
 * @param {winston.Logger} logger - Logger instance
 * @param {string} event - Security event type
 * @param {Object} data - Event data
 */
const logSecurityEvent = (logger, event, data) => {
  logger.warn({
    message: `Security event: ${event}`,
    event,
    data,
    timestamp: new Date().toISOString(),
    securityLog: true
  });
};

module.exports = {
  setupLogging,
  logSecurityEvent
};