Imports Microsoft.VisualBasic.ApplicationServices
Imports System.Windows.Forms

Namespace My
    ' The following events are available for MyApplication:
    ' Startup: Raised when the application starts, before the startup form is created.
    ' Shutdown: Raised after all application forms are closed.  This event is not raised if the application terminates abnormally.
    ' UnhandledException: Raised if the application encounters an unhandled exception.
    ' StartupNextInstance: Raised when launching a single-instance application and the application is already active.
    ' NetworkAvailabilityChanged: Raised when the network connection is connected or disconnected.
    Partial Friend Class MyApplication
        Private Sub MyApplication_Startup(sender As Object, e As StartupEventArgs) Handles Me.Startup
            ' Initialize application settings and configurations
            ' Set up global exception handling
            AddHandler Application.ThreadException, AddressOf Application_ThreadException
            AddHandler AppDomain.CurrentDomain.UnhandledException, AddressOf CurrentDomain_UnhandledException
        End Sub

        Private Sub CurrentDomain_UnhandledException(sender As Object, e As UnhandledExceptionEventArgs)
            Dim ex As Exception = CType(e.ExceptionObject, Exception)
            LogException(ex)
            MessageBox.Show("A fatal error occurred: " & ex.Message & Environment.NewLine & Environment.NewLine &
                           "The application will now close. Please contact support with the following information:" & Environment.NewLine &
                           "Error: " & ex.GetType().Name & Environment.NewLine &
                           "Details: " & ex.Message,
                           "Fatal Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Environment.Exit(1)
        End Sub

        Private Sub Application_ThreadException(sender As Object, e As Threading.ThreadExceptionEventArgs)
            LogException(e.Exception)
            MessageBox.Show("An unexpected error occurred: " & e.Exception.Message & Environment.NewLine & Environment.NewLine &
                           "Please contact support with the following information:" & Environment.NewLine &
                           "Error: " & e.Exception.GetType().Name & Environment.NewLine &
                           "Details: " & e.Exception.Message,
                           "Application Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Sub

        ''' <summary>
        ''' Logs an exception to a file
        ''' </summary>
        Private Sub LogException(ex As Exception)
            Try
                ' Create log directory if it doesn't exist
                Dim logDir As String = IO.Path.Combine(Application.StartupPath, "Logs")
                If Not IO.Directory.Exists(logDir) Then
                    IO.Directory.CreateDirectory(logDir)
                End If

                ' Create log file name with date
                Dim logFileName As String = IO.Path.Combine(logDir, "Error_" & DateTime.Now.ToString("yyyyMMdd") & ".log")

                ' Write exception details to log file
                Using writer As New IO.StreamWriter(logFileName, True)
                    writer.WriteLine("===========================================================")
                    writer.WriteLine("Date/Time: " & DateTime.Now.ToString())
                    writer.WriteLine("Exception Type: " & ex.GetType().FullName)
                    writer.WriteLine("Message: " & ex.Message)
                    writer.WriteLine("Stack Trace: " & ex.StackTrace)

                    ' Log inner exception if present
                    Dim innerEx As Exception = ex.InnerException
                    While innerEx IsNot Nothing
                        writer.WriteLine("-----------------------------------------------------------")
                        writer.WriteLine("Inner Exception Type: " & innerEx.GetType().FullName)
                        writer.WriteLine("Inner Exception Message: " & innerEx.Message)
                        writer.WriteLine("Inner Exception Stack Trace: " & innerEx.StackTrace)
                        innerEx = innerEx.InnerException
                    End While

                    writer.WriteLine("===========================================================")
                    writer.WriteLine()
                End Using
            Catch
                ' Ignore errors in the error logger
            End Try
        End Sub
    End Class
End Namespace