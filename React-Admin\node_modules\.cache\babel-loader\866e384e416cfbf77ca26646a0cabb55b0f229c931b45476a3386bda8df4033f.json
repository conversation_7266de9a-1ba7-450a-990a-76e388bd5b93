{"ast": null, "code": "import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport LanguageDetector from 'i18next-browser-languagedetector';\nimport Backend from 'i18next-http-backend';\ni18n.use(Backend).use(LanguageDetector).use(initReactI18next).init({\n  fallbackLng: 'en',\n  debug: true,\n  supportedLngs: ['en', 'ar'],\n  interpolation: {\n    escapeValue: false\n  },\n  backend: {\n    loadPath: '/locales/{{lng}}/{{ns}}.json'\n  }\n});\nexport default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "LanguageDetector", "Backend", "use", "init", "fallbackLng", "debug", "supportedLngs", "interpolation", "escapeValue", "backend", "loadPath"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/src/i18n/config.js"], "sourcesContent": ["import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport LanguageDetector from 'i18next-browser-languagedetector';\nimport Backend from 'i18next-http-backend';\n\ni18n\n  .use(Backend)\n  .use(LanguageDetector)\n  .use(initReactI18next)\n  .init({\n    fallbackLng: 'en',\n    debug: true,\n    supportedLngs: ['en', 'ar'],\n    interpolation: {\n      escapeValue: false,\n    },\n    backend: {\n      loadPath: '/locales/{{lng}}/{{ns}}.json',\n    },\n  });\n\nexport default i18n;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,SAASC,gBAAgB,QAAQ,eAAe;AAChD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,OAAO,MAAM,sBAAsB;AAE1CH,IAAI,CACDI,GAAG,CAACD,OAAO,CAAC,CACZC,GAAG,CAACF,gBAAgB,CAAC,CACrBE,GAAG,CAACH,gBAAgB,CAAC,CACrBI,IAAI,CAAC;EACJC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,IAAI;EACXC,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAC3BC,aAAa,EAAE;IACbC,WAAW,EAAE;EACf,CAAC;EACDC,OAAO,EAAE;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEJ,eAAeZ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}