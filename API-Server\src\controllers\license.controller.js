/**
 * License controller for the License Verification API
 * Handles license activation, verification, and management
 */

const { v4: uuidv4 } = require('uuid');
const { setupLogging, logSecurityEvent } = require('../utils/logger');
const { getSupabaseClient } = require('../config/supabase');
const licenseService = require('../services/license.service');
const cryptoService = require('../services/crypto.service');

// Initialize logger
const logger = setupLogging();

/**
 * Activate a license with hardware fingerprint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const activateLicense = async (req, res) => {
  try {
    const { licenseKey, hardwareFingerprint, clientInfo } = req.body;
    
    if (!licenseKey || !hardwareFingerprint) {
      return res.status(400).json({ error: 'License key and hardware fingerprint are required' });
    }
    
    // Validate license key format
    if (!licenseService.isValidLicenseKeyFormat(licenseKey)) {
      logSecurityEvent(logger, 'INVALID_LICENSE_FORMAT', { licenseKey, ip: req.ip });
      return res.status(400).json({ error: 'Invalid license key format' });
    }
    
    // Get license from database
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseKey)
      .single();
    
    if (error || !license) {
      logSecurityEvent(logger, 'LICENSE_NOT_FOUND', { licenseKey, ip: req.ip });
      return res.status(404).json({ error: 'License not found' });
    }
    
    // Check if license is active
    if (!license.is_active) {
      logSecurityEvent(logger, 'INACTIVE_LICENSE', { licenseKey, ip: req.ip });
      return res.status(403).json({ error: 'License is inactive' });
    }
    
    // Check if license has expired
    if (license.expiration_date && new Date(license.expiration_date) < new Date()) {
      logSecurityEvent(logger, 'EXPIRED_LICENSE', { licenseKey, ip: req.ip });
      
      // Update license status to inactive
      await supabase
        .from('licenses')
        .update({ is_active: false })
        .eq('id', license.id);
      
      return res.status(403).json({ error: 'License has expired' });
    }
    
    // Check if hardware fingerprint is already registered
    if (license.hardware_fingerprint && license.hardware_fingerprint !== hardwareFingerprint) {
      // If hardware transfer is allowed, update the fingerprint
      if (license.allow_hardware_transfer) {
        await supabase
          .from('licenses')
          .update({ hardware_fingerprint: hardwareFingerprint, last_hardware_change: new Date().toISOString() })
          .eq('id', license.id);
        
        logger.info(`Hardware fingerprint updated for license ${licenseKey}`);
      } else {
        logSecurityEvent(logger, 'HARDWARE_MISMATCH', { 
          licenseKey, 
          registeredFingerprint: license.hardware_fingerprint,
          attemptedFingerprint: hardwareFingerprint,
          ip: req.ip 
        });
        return res.status(403).json({ error: 'License is already activated on another device' });
      }
    } else if (!license.hardware_fingerprint) {
      // First activation, register the hardware fingerprint
      await supabase
        .from('licenses')
        .update({ 
          hardware_fingerprint: hardwareFingerprint,
          activation_date: new Date().toISOString(),
          last_hardware_change: new Date().toISOString()
        })
        .eq('id', license.id);
      
      logger.info(`License ${licenseKey} activated for the first time`);
    }
    
    // Generate signed license data
    const licenseData = {
      licenseKey,
      hardwareFingerprint,
      permissions: license.permissions,
      expirationDate: license.expiration_date,
      customerName: license.customer_name,
      customerType: license.customer_type,
      timestamp: new Date().toISOString()
    };
    
    // Sign the license data
    const signedLicense = await cryptoService.signLicenseData(licenseData);
    
    // Log client information
    if (clientInfo) {
      await supabase
        .from('license_activations')
        .insert({
          license_id: license.id,
          hardware_fingerprint: hardwareFingerprint,
          client_ip: req.ip,
          client_info: clientInfo,
          activation_date: new Date().toISOString()
        });
    }
    
    // Return signed license data
    return res.status(200).json({
      success: true,
      message: 'License activated successfully',
      license: signedLicense
    });
  } catch (error) {
    logger.error(`License activation error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Verify a license is valid and active
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifyLicense = async (req, res) => {
  try {
    const { licenseKey, hardwareFingerprint, signedData } = req.body;
    
    if (!licenseKey || !hardwareFingerprint || !signedData) {
      return res.status(400).json({ error: 'License key, hardware fingerprint, and signed data are required' });
    }
    
    // Verify signature
    const isValidSignature = await cryptoService.verifySignature(signedData, licenseKey, hardwareFingerprint);
    if (!isValidSignature) {
      logSecurityEvent(logger, 'INVALID_SIGNATURE', { licenseKey, ip: req.ip });
      return res.status(403).json({ error: 'Invalid license signature' });
    }
    
    // Get license from database
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseKey)
      .single();
    
    if (error || !license) {
      logSecurityEvent(logger, 'LICENSE_NOT_FOUND', { licenseKey, ip: req.ip });
      return res.status(404).json({ error: 'License not found' });
    }
    
    // Check if license is active
    if (!license.is_active) {
      logSecurityEvent(logger, 'INACTIVE_LICENSE', { licenseKey, ip: req.ip });
      return res.status(403).json({ error: 'License is inactive' });
    }
    
    // Check if license has expired
    if (license.expiration_date && new Date(license.expiration_date) < new Date()) {
      logSecurityEvent(logger, 'EXPIRED_LICENSE', { licenseKey, ip: req.ip });
      
      // Update license status to inactive
      await supabase
        .from('licenses')
        .update({ is_active: false })
        .eq('id', license.id);
      
      return res.status(403).json({ error: 'License has expired' });
    }
    
    // Check if hardware fingerprint matches
    if (license.hardware_fingerprint !== hardwareFingerprint) {
      logSecurityEvent(logger, 'HARDWARE_MISMATCH', { 
        licenseKey, 
        registeredFingerprint: license.hardware_fingerprint,
        attemptedFingerprint: hardwareFingerprint,
        ip: req.ip 
      });
      return res.status(403).json({ error: 'Hardware fingerprint mismatch' });
    }
    
    // Log verification
    await supabase
      .from('license_verifications')
      .insert({
        license_id: license.id,
        hardware_fingerprint: hardwareFingerprint,
        client_ip: req.ip,
        verification_date: new Date().toISOString(),
        is_valid: true
      });
    
    // Return license data
    return res.status(200).json({
      success: true,
      message: 'License is valid',
      license: {
        permissions: license.permissions,
        expirationDate: license.expiration_date,
        customerName: license.customer_name,
        customerType: license.customer_type
      }
    });
  } catch (error) {
    logger.error(`License verification error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Revoke a license
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const revokeLicense = async (req, res) => {
  try {
    const { licenseKey } = req.body;
    
    if (!licenseKey) {
      return res.status(400).json({ error: 'License key is required' });
    }
    
    // Get license from database
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseKey)
      .single();
    
    if (error || !license) {
      return res.status(404).json({ error: 'License not found' });
    }
    
    // Update license status to inactive
    const { data, updateError } = await supabase
      .from('licenses')
      .update({ 
        is_active: false,
        revocation_date: new Date().toISOString(),
        revoked_by: req.user.id
      })
      .eq('id', license.id);
    
    if (updateError) {
      logger.error(`License revocation error: ${updateError.message}`);
      return res.status(500).json({ error: 'Failed to revoke license' });
    }
    
    logger.info(`License ${licenseKey} revoked by user ${req.user.email}`);
    
    return res.status(200).json({
      success: true,
      message: 'License revoked successfully'
    });
  } catch (error) {
    logger.error(`License revocation error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Get all licenses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllLicenses = async (req, res) => {
  try {
    const supabase = getSupabaseClient();
    const { data: licenses, error } = await supabase
      .from('licenses')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      logger.error(`Error fetching licenses: ${error.message}`);
      return res.status(500).json({ error: 'Failed to fetch licenses' });
    }
    
    return res.status(200).json({
      success: true,
      licenses
    });
  } catch (error) {
    logger.error(`Error fetching licenses: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Get license by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getLicenseById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select(`
        *,
        license_activations(*),
        license_verifications(*)
      `)
      .eq('id', id)
      .single();
    
    if (error || !license) {
      return res.status(404).json({ error: 'License not found' });
    }
    
    return res.status(200).json({
      success: true,
      license
    });
  } catch (error) {
    logger.error(`Error fetching license: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Create a new license
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createLicense = async (req, res) => {
  try {
    const { 
      customerName, 
      customerEmail,
      customerType,
      permissions,
      expirationDays,
      allowHardwareTransfer
    } = req.body;
    
    if (!customerName || !customerEmail || !customerType || !permissions) {
      return res.status(400).json({ error: 'Customer name, email, type, and permissions are required' });
    }
    
    // Generate license key
    const licenseKey = licenseService.generateLicenseKey();
    
    // Calculate expiration date
    let expirationDate = null;
    if (expirationDays) {
      expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + parseInt(expirationDays));
    }
    
    // Create license in database
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('licenses')
      .insert({
        license_key: licenseKey,
        customer_name: customerName,
        customer_email: customerEmail,
        customer_type: customerType,
        permissions,
        expiration_date: expirationDate,
        is_active: true,
        allow_hardware_transfer: allowHardwareTransfer || false,
        created_by: req.user.id
      })
      .select()
      .single();
    
    if (error) {
      logger.error(`License creation error: ${error.message}`);
      return res.status(500).json({ error: 'Failed to create license' });
    }
    
    logger.info(`New license ${licenseKey} created for ${customerName} by ${req.user.email}`);
    
    return res.status(201).json({
      success: true,
      message: 'License created successfully',
      license: data
    });
  } catch (error) {
    logger.error(`License creation error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Update a license
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateLicense = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      customerName, 
      customerEmail,
      customerType,
      permissions,
      expirationDays,
      isActive,
      allowHardwareTransfer,
      resetHardwareFingerprint
    } = req.body;
    
    // Get license from database
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error || !license) {
      return res.status(404).json({ error: 'License not found' });
    }
    
    // Prepare update data
    const updateData = {};
    
    if (customerName) updateData.customer_name = customerName;
    if (customerEmail) updateData.customer_email = customerEmail;
    if (customerType) updateData.customer_type = customerType;
    if (permissions) updateData.permissions = permissions;
    if (typeof isActive === 'boolean') updateData.is_active = isActive;
    if (typeof allowHardwareTransfer === 'boolean') updateData.allow_hardware_transfer = allowHardwareTransfer;
    
    // Calculate new expiration date if provided
    if (expirationDays) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + parseInt(expirationDays));
      updateData.expiration_date = expirationDate.toISOString();
    }
    
    // Reset hardware fingerprint if requested
    if (resetHardwareFingerprint) {
      updateData.hardware_fingerprint = null;
      updateData.activation_date = null;
      updateData.last_hardware_change = null;
    }
    
    // Update license in database
    const { data, updateError } = await supabase
      .from('licenses')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
        updated_by: req.user.id
      })
      .eq('id', id)
      .select()
      .single();
    
    if (updateError) {
      logger.error(`License update error: ${updateError.message}`);
      return res.status(500).json({ error: 'Failed to update license' });
    }
    
    logger.info(`License ${license.license_key} updated by ${req.user.email}`);
    
    return res.status(200).json({
      success: true,
      message: 'License updated successfully',
      license: data
    });
  } catch (error) {
    logger.error(`License update error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Delete a license
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteLicense = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get license from database
    const supabase = getSupabaseClient();
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error || !license) {
      return res.status(404).json({ error: 'License not found' });
    }
    
    // Delete license from database
    const { error: deleteError } = await supabase
      .from('licenses')
      .delete()
      .eq('id', id);
    
    if (deleteError) {
      logger.error(`License deletion error: ${deleteError.message}`);
      return res.status(500).json({ error: 'Failed to delete license' });
    }
    
    logger.info(`License ${license.license_key} deleted by ${req.user.email}`);
    
    return res.status(200).json({
      success: true,
      message: 'License deleted successfully'
    });
  } catch (error) {
    logger.error(`License deletion error: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  activateLicense,
  verifyLicense,
  revokeLicense,
  getAllLicenses,
  getLicenseById,
  createLicense,
  updateLicense,
  deleteLicense
};