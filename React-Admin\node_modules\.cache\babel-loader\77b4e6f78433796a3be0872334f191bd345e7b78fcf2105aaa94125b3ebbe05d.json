{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New\\\\React-Admin\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport './i18n/config';\nimport DashboardLayout from './components/DashboardLayout';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const {\n    i18n\n  } = useTranslation();\n  React.useEffect(() => {\n    // Set the dir attribute on the html tag based on the current language\n    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\n    // Set the lang attribute\n    document.documentElement.lang = i18n.language;\n  }, [i18n.language]);\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(DashboardLayout, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"XIFq83ieJr2IDBjDrSkk+Qj6jto=\", false, function () {\n  return [useTranslation];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "useTranslation", "DashboardLayout", "jsxDEV", "_jsxDEV", "App", "_s", "i18n", "useEffect", "document", "documentElement", "dir", "language", "lang", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>rowserRouter as Router } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport './i18n/config';\nimport DashboardLayout from './components/DashboardLayout';\n\nfunction App() {\n  const { i18n } = useTranslation();\n\n  React.useEffect(() => {\n    // Set the dir attribute on the html tag based on the current language\n    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\n    // Set the lang attribute\n    document.documentElement.lang = i18n.language;\n  }, [i18n.language]);\n\n  return (\n    <Router>\n      <div className=\"min-h-screen bg-gray-50\">\n        <DashboardLayout />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,QAAQ,kBAAkB;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAO,eAAe;AACtB,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAK,CAAC,GAAGN,cAAc,CAAC,CAAC;EAEjCH,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB;IACAC,QAAQ,CAACC,eAAe,CAACC,GAAG,GAAGJ,IAAI,CAACK,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK;IACrE;IACAH,QAAQ,CAACC,eAAe,CAACG,IAAI,GAAGN,IAAI,CAACK,QAAQ;EAC/C,CAAC,EAAE,CAACL,IAAI,CAACK,QAAQ,CAAC,CAAC;EAEnB,oBACER,OAAA,CAACJ,MAAM;IAAAc,QAAA,eACLV,OAAA;MAAKW,SAAS,EAAC,yBAAyB;MAAAD,QAAA,eACtCV,OAAA,CAACF,eAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACb,EAAA,CAjBQD,GAAG;EAAA,QACOJ,cAAc;AAAA;AAAAmB,EAAA,GADxBf,GAAG;AAmBZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}