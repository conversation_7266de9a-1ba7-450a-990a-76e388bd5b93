{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New\\\\React-Admin\\\\src\\\\components\\\\DashboardLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { HomeIcon, KeyIcon, ShieldExclamationIcon, UserIcon, MenuIcon, XIcon, TranslateIcon } from '@heroicons/react/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DashboardLayout() {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigation = [{\n    name: t('common.home'),\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: t('common.licenses'),\n    href: '/licenses',\n    icon: KeyIcon\n  }, {\n    name: t('common.security'),\n    href: '/security-alerts',\n    icon: ShieldExclamationIcon\n  }, {\n    name: t('common.profile'),\n    href: '/profile',\n    icon: UserIcon\n  }];\n  const toggleLanguage = () => {\n    const newLang = i18n.language === 'ar' ? 'en' : 'ar';\n    i18n.changeLanguage(newLang);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-0 z-40 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 -mr-12 pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n            onClick: () => setSidebarOpen(false),\n            children: /*#__PURE__*/_jsxDEV(XIcon, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex-1 px-2 pb-4 space-y-1\",\n          children: navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            className: \"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\",\n            children: [/*#__PURE__*/_jsxDEV(item.icon, {\n              className: \"mr-4 h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this), item.name]\n          }, item.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:flex lg:flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col w-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex-1 px-2 space-y-1\",\n              children: navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: \"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"mr-3 h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 21\n                }, this), item.name]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden\",\n          onClick: () => setSidebarOpen(true),\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 px-4 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4 flex items-center md:ml-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleLanguage,\n              className: \"p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n              children: /*#__PURE__*/_jsxDEV(TranslateIcon, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {/* Add logout handler */},\n              className: \"ml-3 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\",\n              children: t('common.logout')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardLayout, \"pZK+n/nJvghYBrS6AXQTvtil760=\", false, function () {\n  return [useTranslation];\n});\n_c = DashboardLayout;\nexport default DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "Link", "HomeIcon", "KeyIcon", "ShieldExclamationIcon", "UserIcon", "MenuIcon", "XIcon", "TranslateIcon", "jsxDEV", "_jsxDEV", "DashboardLayout", "_s", "sidebarOpen", "setSidebarOpen", "t", "i18n", "navigation", "name", "href", "icon", "toggleLanguage", "newLang", "language", "changeLanguage", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/src/components/DashboardLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { HomeIcon, KeyIcon, ShieldExclamationIcon, UserIcon, MenuIcon, XIcon, TranslateIcon } from '@heroicons/react/outline';\n\nfunction DashboardLayout() {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { t, i18n } = useTranslation();\n\n  const navigation = [\n    { name: t('common.home'), href: '/', icon: HomeIcon },\n    { name: t('common.licenses'), href: '/licenses', icon: KeyIcon },\n    { name: t('common.security'), href: '/security-alerts', icon: ShieldExclamationIcon },\n    { name: t('common.profile'), href: '/profile', icon: UserIcon },\n  ];\n\n  const toggleLanguage = () => {\n    const newLang = i18n.language === 'ar' ? 'en' : 'ar';\n    i18n.changeLanguage(newLang);\n  };\n\n  return (\n    <div className=\"flex h-screen overflow-hidden\">\n      {/* Sidebar for mobile */}\n      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                to={item.href}\n                className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n              >\n                <item.icon className=\"mr-4 h-6 w-6\" />\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Static sidebar for desktop */}\n      <div className=\"hidden lg:flex lg:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n            <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n              <nav className=\"flex-1 px-2 space-y-1\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                  >\n                    <item.icon className=\"mr-3 h-6 w-6\" />\n                    {item.name}\n                  </Link>\n                ))}\n              </nav>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <MenuIcon className=\"h-6 w-6\" />\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1\"></div>\n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <button\n                onClick={toggleLanguage}\n                className=\"p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                <TranslateIcon className=\"h-6 w-6\" />\n              </button>\n              <button\n                onClick={() => {/* Add logout handler */}}\n                className=\"ml-3 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\"\n              >\n                {t('common.logout')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {/* Add your page content here */}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n\nexport default DashboardLayout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9H,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEgB,CAAC;IAAEC;EAAK,CAAC,GAAGhB,cAAc,CAAC,CAAC;EAEpC,MAAMiB,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAEH,CAAC,CAAC,aAAa,CAAC;IAAEI,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAElB;EAAS,CAAC,EACrD;IAAEgB,IAAI,EAAEH,CAAC,CAAC,iBAAiB,CAAC;IAAEI,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEjB;EAAQ,CAAC,EAChE;IAAEe,IAAI,EAAEH,CAAC,CAAC,iBAAiB,CAAC;IAAEI,IAAI,EAAE,kBAAkB;IAAEC,IAAI,EAAEhB;EAAsB,CAAC,EACrF;IAAEc,IAAI,EAAEH,CAAC,CAAC,gBAAgB,CAAC;IAAEI,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEf;EAAS,CAAC,CAChE;EAED,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAO,GAAGN,IAAI,CAACO,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;IACpDP,IAAI,CAACQ,cAAc,CAACF,OAAO,CAAC;EAC9B,CAAC;EAED,oBACEZ,OAAA;IAAKe,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5ChB,OAAA;MAAKe,SAAS,EAAE,gCAAgCZ,WAAW,GAAG,OAAO,GAAG,QAAQ,EAAG;MAAAa,QAAA,gBACjFhB,OAAA;QAAKe,SAAS,EAAC,yCAAyC;QAACE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,KAAK;MAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrGrB,OAAA;QAAKe,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEhB,OAAA;UAAKe,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDhB,OAAA;YACEe,SAAS,EAAC,gIAAgI;YAC1IE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,KAAK,CAAE;YAAAY,QAAA,eAErChB,OAAA,CAACH,KAAK;cAACkB,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrB,OAAA;UAAKe,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACxCT,UAAU,CAACe,GAAG,CAAEC,IAAI,iBACnBvB,OAAA,CAACT,IAAI;YAEHiC,EAAE,EAAED,IAAI,CAACd,IAAK;YACdM,SAAS,EAAC,uHAAuH;YAAAC,QAAA,gBAEjIhB,OAAA,CAACuB,IAAI,CAACb,IAAI;cAACK,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACrCE,IAAI,CAACf,IAAI;UAAA,GALLe,IAAI,CAACf,IAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrB,OAAA;MAAKe,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9ChB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjChB,OAAA;UAAKe,SAAS,EAAC,gEAAgE;UAAAC,QAAA,eAC7EhB,OAAA;YAAKe,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DhB,OAAA;cAAKe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCT,UAAU,CAACe,GAAG,CAAEC,IAAI,iBACnBvB,OAAA,CAACT,IAAI;gBAEHiC,EAAE,EAAED,IAAI,CAACd,IAAK;gBACdM,SAAS,EAAC,qHAAqH;gBAAAC,QAAA,gBAE/HhB,OAAA,CAACuB,IAAI,CAACb,IAAI;kBAACK,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrCE,IAAI,CAACf,IAAI;cAAA,GALLe,IAAI,CAACf,IAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrB,OAAA;MAAKe,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDhB,OAAA;QAAKe,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpEhB,OAAA;UACEe,SAAS,EAAC,+HAA+H;UACzIE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,IAAI,CAAE;UAAAY,QAAA,eAEpChB,OAAA,CAACJ,QAAQ;YAACmB,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACTrB,OAAA;UAAKe,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/ChB,OAAA;YAAKe,SAAS,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9BrB,OAAA;YAAKe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChB,OAAA;cACEiB,OAAO,EAAEN,cAAe;cACxBI,SAAS,EAAC,+HAA+H;cAAAC,QAAA,eAEzIhB,OAAA,CAACF,aAAa;gBAACiB,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACTrB,OAAA;cACEiB,OAAO,EAAEA,CAAA,KAAM,CAAC,yBAA0B;cAC1CF,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAE/EX,CAAC,CAAC,eAAe;YAAC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrB,OAAA;QAAMe,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eAClEhB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBhB,OAAA;YAAKe,SAAS,EAAC;UAAwC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnB,EAAA,CAzGQD,eAAe;EAAA,QAEFX,cAAc;AAAA;AAAAmC,EAAA,GAF3BxB,eAAe;AA2GxB,eAAeA,eAAe;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}