import React from 'react';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import './i18n/config';
import DashboardLayout from './components/DashboardLayout';

function App() {
  const { i18n } = useTranslation();

  React.useEffect(() => {
    // Set the dir attribute on the html tag based on the current language
    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
    // Set the lang attribute
    document.documentElement.lang = i18n.language;
  }, [i18n.language]);

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <DashboardLayout />
      </div>
    </Router>
  );
}

export default App;