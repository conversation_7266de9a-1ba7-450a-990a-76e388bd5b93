/**
 * Authentication middleware for the License Verification API
 * Verifies JWT tokens and user roles
 */

const jwt = require('jsonwebtoken');
const { setupLogging } = require('../utils/logger');
const { getSupabaseClient } = require('../config/supabase');

// Initialize logger
const logger = setupLogging();

/**
 * Middleware to verify JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }
    
    const token = authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. Invalid token format.' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    
    // Check if token is blacklisted (logged out)
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('blacklisted_tokens')
      .select('*')
      .eq('token', token)
      .single();
    
    if (data) {
      return res.status(401).json({ error: 'Token has been invalidated.' });
    }
    
    next();
  } catch (error) {
    logger.error(`Token verification failed: ${error.message}`);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired.' });
    }
    
    return res.status(401).json({ error: 'Invalid token.' });
  }
};

/**
 * Middleware to check if user is an admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const isAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated.' });
    }
    
    if (req.user.role !== 'admin' && req.user.role !== 'superadmin') {
      return res.status(403).json({ error: 'Access denied. Admin role required.' });
    }
    
    next();
  } catch (error) {
    logger.error(`Admin verification failed: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

/**
 * Middleware to check if user is a super admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const isSuperAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated.' });
    }
    
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ error: 'Access denied. Super Admin role required.' });
    }
    
    next();
  } catch (error) {
    logger.error(`Super Admin verification failed: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

module.exports = {
  verifyToken,
  isAdmin,
  isSuperAdmin
};