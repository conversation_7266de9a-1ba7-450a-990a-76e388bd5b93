{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "surrogate", "unicodeEscape", "escape", "code", "str", "toString", "length", "replacer", "match", "backslashes", "char", "String", "fromCodePoint", "parseInt", "escaped", "slice", "charCodeAt", "replaceUnicodeEscapes", "replace", "getUnicodeEscape", "exec", "lastIndex", "name", "manipulateOptions", "generatorOpts", "_generatorOpts$jsescO", "_generatorOpts$jsescO2", "jsescOption", "minimal", "visitor", "Identifier", "path", "node", "key", "replaced", "c", "t", "inherits", "stringLiteral", "replaceWith", "parentPath", "scope", "isMemberExpression", "property", "isOptionalMemberExpression", "computed", "binding", "getBinding", "rename", "generateUid", "buildCodeFrameError", "StringLiteral|DirectiveLiteral", "extra", "raw", "TemplateElement", "value", "firstEscape", "grandParent", "isTaggedTemplateExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const surrogate = /[\\ud800-\\udfff]/g;\n  const unicodeEscape = /(\\\\+)u\\{([0-9a-fA-F]+)\\}/g;\n\n  function escape(code: number) {\n    if (process.env.BABEL_8_BREAKING) {\n      return \"\\\\u\" + code.toString(16).padStart(4, \"0\");\n    } else {\n      let str = code.toString(16);\n      while (str.length < 4) str = \"0\" + str;\n      return \"\\\\u\" + str;\n    }\n  }\n\n  function replacer(match: string, backslashes: string, code: string) {\n    if (backslashes.length % 2 === 0) {\n      return match;\n    }\n\n    const char = String.fromCodePoint(parseInt(code, 16));\n    const escaped = backslashes.slice(0, -1) + escape(char.charCodeAt(0));\n\n    return char.length === 1 ? escaped : escaped + escape(char.charCodeAt(1));\n  }\n\n  function replaceUnicodeEscapes(str: string) {\n    return str.replace(unicodeEscape, replacer);\n  }\n\n  function getUnicodeEscape(str: string) {\n    let match;\n    while ((match = unicodeEscape.exec(str))) {\n      if (match[1].length % 2 === 0) continue;\n      unicodeEscape.lastIndex = 0;\n      return match[0];\n    }\n    return null;\n  }\n\n  return {\n    name: \"transform-unicode-escapes\",\n    manipulateOptions({ generatorOpts }) {\n      // Babel 8 will enable jsesc minimal mode by default, which outputs\n      // unescaped unicode string\n      if (!generatorOpts.jsescOption) {\n        generatorOpts.jsescOption = {};\n      }\n      generatorOpts.jsescOption.minimal ??= false;\n    },\n    visitor: {\n      Identifier(path) {\n        const { node, key } = path;\n        const { name } = node;\n        const replaced = name.replace(surrogate, c => {\n          return `_u${c.charCodeAt(0).toString(16)}`;\n        });\n        if (name === replaced) return;\n\n        const str = t.inherits(t.stringLiteral(name), node);\n\n        if (key === \"key\") {\n          path.replaceWith(str);\n          return;\n        }\n\n        const { parentPath, scope } = path;\n        if (\n          parentPath.isMemberExpression({ property: node }) ||\n          parentPath.isOptionalMemberExpression({ property: node })\n        ) {\n          parentPath.node.computed = true;\n          path.replaceWith(str);\n          return;\n        }\n\n        const binding = scope.getBinding(name);\n        if (binding) {\n          scope.rename(name, scope.generateUid(replaced));\n          return;\n        }\n\n        throw path.buildCodeFrameError(\n          `Can't reference '${name}' as a bare identifier`,\n        );\n      },\n\n      \"StringLiteral|DirectiveLiteral\"(\n        path: NodePath<t.StringLiteral | t.DirectiveLiteral>,\n      ) {\n        const { node } = path;\n        const { extra } = node;\n\n        if (extra?.raw) extra.raw = replaceUnicodeEscapes(extra.raw as string);\n      },\n\n      TemplateElement(path) {\n        const { node, parentPath } = path;\n        const { value } = node;\n\n        const firstEscape = getUnicodeEscape(value.raw);\n        if (!firstEscape) return;\n\n        const grandParent = parentPath.parentPath;\n        if (grandParent.isTaggedTemplateExpression()) {\n          throw path.buildCodeFrameError(\n            `Can't replace Unicode escape '${firstEscape}' inside tagged template literals. You can enable '@babel/plugin-transform-template-literals' to compile them to classic strings.`,\n          );\n        }\n\n        value.raw = replaceUnicodeEscapes(value.raw);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAwD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEzC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,SAAS,GAAG,kBAAkB;EACpC,MAAMC,aAAa,GAAG,2BAA2B;EAEjD,SAASC,MAAMA,CAACC,IAAY,EAAE;IAGrB;MACL,IAAIC,GAAG,GAAGD,IAAI,CAACE,QAAQ,CAAC,EAAE,CAAC;MAC3B,OAAOD,GAAG,CAACE,MAAM,GAAG,CAAC,EAAEF,GAAG,GAAG,GAAG,GAAGA,GAAG;MACtC,OAAO,KAAK,GAAGA,GAAG;IACpB;EACF;EAEA,SAASG,QAAQA,CAACC,KAAa,EAAEC,WAAmB,EAAEN,IAAY,EAAE;IAClE,IAAIM,WAAW,CAACH,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MAChC,OAAOE,KAAK;IACd;IAEA,MAAME,IAAI,GAAGC,MAAM,CAACC,aAAa,CAACC,QAAQ,CAACV,IAAI,EAAE,EAAE,CAAC,CAAC;IACrD,MAAMW,OAAO,GAAGL,WAAW,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGb,MAAM,CAACQ,IAAI,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC;IAErE,OAAON,IAAI,CAACJ,MAAM,KAAK,CAAC,GAAGQ,OAAO,GAAGA,OAAO,GAAGZ,MAAM,CAACQ,IAAI,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC;EAC3E;EAEA,SAASC,qBAAqBA,CAACb,GAAW,EAAE;IAC1C,OAAOA,GAAG,CAACc,OAAO,CAACjB,aAAa,EAAEM,QAAQ,CAAC;EAC7C;EAEA,SAASY,gBAAgBA,CAACf,GAAW,EAAE;IACrC,IAAII,KAAK;IACT,OAAQA,KAAK,GAAGP,aAAa,CAACmB,IAAI,CAAChB,GAAG,CAAC,EAAG;MACxC,IAAII,KAAK,CAAC,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MAC/BL,aAAa,CAACoB,SAAS,GAAG,CAAC;MAC3B,OAAOb,KAAK,CAAC,CAAC,CAAC;IACjB;IACA,OAAO,IAAI;EACb;EAEA,OAAO;IACLc,IAAI,EAAE,2BAA2B;IACjCC,iBAAiBA,CAAC;MAAEC;IAAc,CAAC,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MAGnC,IAAI,CAACF,aAAa,CAACG,WAAW,EAAE;QAC9BH,aAAa,CAACG,WAAW,GAAG,CAAC,CAAC;MAChC;MACA,CAAAD,sBAAA,IAAAD,qBAAA,GAAAD,aAAa,CAACG,WAAW,EAACC,OAAO,YAAAF,sBAAA,GAAjCD,qBAAA,CAA0BG,OAAO,GAAK,KAAK;IAC7C,CAAC;IACDC,OAAO,EAAE;MACPC,UAAUA,CAACC,IAAI,EAAE;QACf,MAAM;UAAEC,IAAI;UAAEC;QAAI,CAAC,GAAGF,IAAI;QAC1B,MAAM;UAAET;QAAK,CAAC,GAAGU,IAAI;QACrB,MAAME,QAAQ,GAAGZ,IAAI,CAACJ,OAAO,CAAClB,SAAS,EAAEmC,CAAC,IAAI;UAC5C,OAAO,KAAKA,CAAC,CAACnB,UAAU,CAAC,CAAC,CAAC,CAACX,QAAQ,CAAC,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC;QACF,IAAIiB,IAAI,KAAKY,QAAQ,EAAE;QAEvB,MAAM9B,GAAG,GAAGgC,WAAC,CAACC,QAAQ,CAACD,WAAC,CAACE,aAAa,CAAChB,IAAI,CAAC,EAAEU,IAAI,CAAC;QAEnD,IAAIC,GAAG,KAAK,KAAK,EAAE;UACjBF,IAAI,CAACQ,WAAW,CAACnC,GAAG,CAAC;UACrB;QACF;QAEA,MAAM;UAAEoC,UAAU;UAAEC;QAAM,CAAC,GAAGV,IAAI;QAClC,IACES,UAAU,CAACE,kBAAkB,CAAC;UAAEC,QAAQ,EAAEX;QAAK,CAAC,CAAC,IACjDQ,UAAU,CAACI,0BAA0B,CAAC;UAAED,QAAQ,EAAEX;QAAK,CAAC,CAAC,EACzD;UACAQ,UAAU,CAACR,IAAI,CAACa,QAAQ,GAAG,IAAI;UAC/Bd,IAAI,CAACQ,WAAW,CAACnC,GAAG,CAAC;UACrB;QACF;QAEA,MAAM0C,OAAO,GAAGL,KAAK,CAACM,UAAU,CAACzB,IAAI,CAAC;QACtC,IAAIwB,OAAO,EAAE;UACXL,KAAK,CAACO,MAAM,CAAC1B,IAAI,EAAEmB,KAAK,CAACQ,WAAW,CAACf,QAAQ,CAAC,CAAC;UAC/C;QACF;QAEA,MAAMH,IAAI,CAACmB,mBAAmB,CAC5B,oBAAoB5B,IAAI,wBAC1B,CAAC;MACH,CAAC;MAED,gCAAgC6B,CAC9BpB,IAAoD,EACpD;QACA,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,MAAM;UAAEqB;QAAM,CAAC,GAAGpB,IAAI;QAEtB,IAAIoB,KAAK,YAALA,KAAK,CAAEC,GAAG,EAAED,KAAK,CAACC,GAAG,GAAGpC,qBAAqB,CAACmC,KAAK,CAACC,GAAa,CAAC;MACxE,CAAC;MAEDC,eAAeA,CAACvB,IAAI,EAAE;QACpB,MAAM;UAAEC,IAAI;UAAEQ;QAAW,CAAC,GAAGT,IAAI;QACjC,MAAM;UAAEwB;QAAM,CAAC,GAAGvB,IAAI;QAEtB,MAAMwB,WAAW,GAAGrC,gBAAgB,CAACoC,KAAK,CAACF,GAAG,CAAC;QAC/C,IAAI,CAACG,WAAW,EAAE;QAElB,MAAMC,WAAW,GAAGjB,UAAU,CAACA,UAAU;QACzC,IAAIiB,WAAW,CAACC,0BAA0B,CAAC,CAAC,EAAE;UAC5C,MAAM3B,IAAI,CAACmB,mBAAmB,CAC5B,iCAAiCM,WAAW,mIAC9C,CAAC;QACH;QAEAD,KAAK,CAACF,GAAG,GAAGpC,qBAAqB,CAACsC,KAAK,CAACF,GAAG,CAAC;MAC9C;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}