Imports System
Imports System.Drawing
Imports System.Drawing.Printing
Imports System.Windows.Forms

Namespace LicenseActivation.Forms
    ''' <summary>
    ''' Form to print license details
    ''' </summary>
    Public Class PrintLicenseForm
        Inherits Form
        Private ReadOnly _license As LicenseActivation.Classes.License
        Private ReadOnly _qrCodeGenerator As LicenseActivation.Classes.QRCodeGenerator
        Private _printDocument As PrintDocument
        Private _printPreviewDialog As PrintPreviewDialog

        ''' <summary>
        ''' Initializes a new instance of the PrintLicenseForm class
        ''' </summary>
        ''' <param name="license">The license to print</param>
        Public Sub New(license As LicenseActivation.Classes.License)
            ' This call is required by the designer
            InitializeComponent()

            ' Set license
            _license = license
            _qrCodeGenerator = New LicenseActivation.Classes.QRCodeGenerator()

            ' Set form properties
            Me.Text = "Print License"
            Me.Icon = My.Resources.AppIcon

            ' Initialize print document
            _printDocument = New PrintDocument()
            AddHandler _printDocument.PrintPage, AddressOf PrintDocument_PrintPage

            ' Initialize print preview dialog
            _printPreviewDialog = New PrintPreviewDialog()
            _printPreviewDialog.Document = _printDocument

            ' Add event handlers
            AddHandler Me.Load, AddressOf PrintLicenseForm_Load
            AddHandler btnPrint.Click, AddressOf BtnPrint_Click
            AddHandler btnClose.Click, AddressOf BtnClose_Click
        End Sub

        ''' <summary>
        ''' Required designer variable
        ''' </summary>
        Private components As System.ComponentModel.IContainer = Nothing

        ''' <summary>
        ''' UI Controls
        ''' </summary>
        Private WithEvents pnlHeader As Panel
        Private WithEvents lblTitle As Label
        Private WithEvents pnlContent As Panel
        Private WithEvents pnlPreview As Panel
        Private WithEvents btnPrint As Button
        Private WithEvents btnClose As Button



        ''' <summary>
        ''' Required method for Designer support - do not modify
        ''' the contents of this method with the code editor
        ''' </summary>
        Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(600, 800)
            Me.StartPosition = FormStartPosition.CenterParent
            Me.FormBorderStyle = FormBorderStyle.FixedDialog
            Me.MaximizeBox = False
            Me.MinimizeBox = False

            ' Create header panel
            pnlHeader = New Panel()
            pnlHeader.Dock = DockStyle.Top
            pnlHeader.Height = 60
            pnlHeader.BackColor = Color.FromArgb(59, 130, 246) ' Blue

            ' Create title label
            lblTitle = New Label()
            lblTitle.Text = "Print License"
            lblTitle.Font = New Font("Segoe UI", 16, FontStyle.Bold)
            lblTitle.ForeColor = Color.White
            lblTitle.AutoSize = True
            lblTitle.Location = New Point(20, 15)

            ' Create content panel
            pnlContent = New Panel()
            pnlContent.Dock = DockStyle.Fill
            pnlContent.Padding = New Padding(20)

            ' Create preview panel
            pnlPreview = New Panel()
            pnlPreview.Size = New Size(500, 650)
            pnlPreview.Location = New Point(50, 20)
            pnlPreview.BorderStyle = BorderStyle.FixedSingle
            pnlPreview.BackColor = Color.White

            ' Create print button
            btnPrint = New Button()
            btnPrint.Text = "Print"
            btnPrint.Width = 150
            btnPrint.Height = 40
            btnPrint.Font = New Font("Segoe UI", 10)
            btnPrint.BackColor = Color.FromArgb(59, 130, 246) ' Blue
            btnPrint.ForeColor = Color.White
            btnPrint.FlatStyle = FlatStyle.Flat
            btnPrint.Location = New Point(150, 700)

            ' Create close button
            btnClose = New Button()
            btnClose.Text = "Close"
            btnClose.Width = 150
            btnClose.Height = 40
            btnClose.Font = New Font("Segoe UI", 10)
            btnClose.BackColor = Color.FromArgb(107, 114, 128) ' Gray
            btnClose.ForeColor = Color.White
            btnClose.FlatStyle = FlatStyle.Flat
            btnClose.Location = New Point(310, 700)

            ' Add controls to header panel
            pnlHeader.Controls.Add(lblTitle)

            ' Add controls to content panel
            pnlContent.Controls.Add(pnlPreview)
            pnlContent.Controls.Add(btnPrint)
            pnlContent.Controls.Add(btnClose)

            ' Add panels to form
            Me.Controls.Add(pnlContent)
            Me.Controls.Add(pnlHeader)
        End Sub

        ''' <summary>
        ''' Handles the form load event
        ''' </summary>
        Private Sub PrintLicenseForm_Load(sender As Object, e As EventArgs)
            ' Draw the license certificate on the preview panel
            DrawLicenseCertificate(pnlPreview.CreateGraphics())

            ' Set up the print preview dialog
            _printPreviewDialog.PrintPreviewControl.Zoom = 1.0
            _printPreviewDialog.WindowState = FormWindowState.Maximized
        End Sub

        ''' <summary>
        ''' Handles the print button click event
        ''' </summary>
        Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
            ' Show print preview dialog
            _printPreviewDialog.ShowDialog()
        End Sub

        ''' <summary>
        ''' Handles the close button click event
        ''' </summary>
        Private Sub BtnClose_Click(sender As Object, e As EventArgs)
            ' Close the form
            Me.Close()
        End Sub

        ''' <summary>
        ''' Handles the print document print page event
        ''' </summary>
        Private Sub PrintDocument_PrintPage(sender As Object, e As PrintPageEventArgs)
            ' Draw the license certificate on the print document
            DrawLicenseCertificate(e.Graphics)
        End Sub

        ''' <summary>
        ''' Draws the license certificate on the specified graphics object
        ''' </summary>
        ''' <param name="g">The graphics object to draw on</param>
        Private Sub DrawLicenseCertificate(g As Graphics)
            ' Set high quality rendering
            g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
            g.TextRenderingHint = Drawing.Text.TextRenderingHint.ClearTypeGridFit

            ' Define certificate dimensions
            Dim certWidth As Integer = 480
            Dim certHeight As Integer = 630
            Dim certX As Integer = 10
            Dim certY As Integer = 10

            ' Create certificate background
            Dim certRect As New Rectangle(certX, certY, certWidth, certHeight)
            Dim certBorderRect As New Rectangle(certX - 1, certY - 1, certWidth + 2, certHeight + 2)

            ' Draw certificate border
            g.DrawRectangle(New Pen(Color.Black, 2), certBorderRect)

            ' Fill certificate background based on license type
            Select Case _license.LicenseType.ToLower()
                Case "standard"
                    g.FillRectangle(New SolidBrush(Color.FromArgb(59, 130, 246)), certRect) ' Blue
                Case "premium"
                    g.FillRectangle(New SolidBrush(Color.FromArgb(139, 92, 246)), certRect) ' Purple
                Case "professional"
                    g.FillRectangle(New SolidBrush(Color.FromArgb(245, 158, 11)), certRect) ' Amber
                Case "enterprise"
                    g.FillRectangle(New SolidBrush(Color.FromArgb(16, 185, 129)), certRect) ' Green
                Case "platinum"
                    ' Apply platinum gradient
                    Dim platinumBrush As Drawing2D.LinearGradientBrush = CreatePlatinumGradientBrush(certRect)
                    g.FillRectangle(platinumBrush, certRect)
                Case "gold"
                    ' Apply gold gradient
                    Dim goldBrush As Drawing2D.LinearGradientBrush = CreateGoldGradientBrush(certRect)
                    g.FillRectangle(goldBrush, certRect)
                Case Else
                    g.FillRectangle(New SolidBrush(Color.FromArgb(107, 114, 128)), certRect) ' Gray
            End Select

            ' Draw certificate header
            Dim headerFont As New Font("Segoe UI", 24, FontStyle.Bold)
            Dim headerBrush As New SolidBrush(Color.White)
            Dim headerRect As New Rectangle(certX + 20, certY + 20, certWidth - 40, 50)
            g.DrawString("License Certificate", headerFont, headerBrush, headerRect, New StringFormat() With {.Alignment = StringAlignment.Center})

            ' Draw company logo if available
            If My.Resources.AppLogo IsNot Nothing Then
                Dim logoRect As New Rectangle(certX + (certWidth / 2) - 50, certY + 80, 100, 100)
                g.DrawImage(My.Resources.AppLogo, logoRect)
            End If

            ' Draw license details
            Dim detailsFont As New Font("Segoe UI", 12)
            Dim detailsBrush As New SolidBrush(Color.White)
            Dim detailsX As Integer = certX + 50
            Dim detailsY As Integer = certY + 200
            Dim detailsLineHeight As Integer = 30

            ' Draw customer name
            g.DrawString("Customer: " & _license.CustomerName, detailsFont, detailsBrush, detailsX, detailsY)
            detailsY += detailsLineHeight

            ' Draw customer email
            g.DrawString("Email: " & _license.CustomerEmail, detailsFont, detailsBrush, detailsX, detailsY)
            detailsY += detailsLineHeight

            ' Draw license type
            g.DrawString("License Type: " & _license.LicenseType, detailsFont, detailsBrush, detailsX, detailsY)
            detailsY += detailsLineHeight

            ' Draw issue date
            g.DrawString("Issue Date: " & LicenseActivation.Modules.Utilities.FormatDate(_license.IssueDate), detailsFont, detailsBrush, detailsX, detailsY)
            detailsY += detailsLineHeight

            ' Draw expiry date
            g.DrawString("Expiry Date: " & LicenseActivation.Modules.Utilities.FormatDate(_license.ExpiryDate), detailsFont, detailsBrush, detailsX, detailsY)
            detailsY += detailsLineHeight * 2

            ' Draw features header
            Dim featuresHeaderFont As New Font("Segoe UI", 14, FontStyle.Bold)
            g.DrawString("Included Features:", featuresHeaderFont, detailsBrush, detailsX, detailsY)
            detailsY += detailsLineHeight

            ' Draw features
            Dim featuresFont As New Font("Segoe UI", 10)
            For Each feature In _license.Features
                g.DrawString("• " & feature, featuresFont, detailsBrush, detailsX + 20, detailsY)
                detailsY += detailsLineHeight - 10
            Next

            ' Generate QR code
            Dim qrCodeData As String = "License Key: " & _license.LicenseKey & vbCrLf &
                                      "Type: " & _license.LicenseType & vbCrLf &
                                      "Customer: " & _license.CustomerName & vbCrLf &
                                      "Expires: " & LicenseActivation.Modules.Utilities.FormatDate(_license.ExpiryDate)

            Dim qrCodeImage As Image = _qrCodeGenerator.GenerateQRCode(qrCodeData, 120, 120)
            Dim qrCodeRect As New Rectangle(certX + certWidth - 150, certY + certHeight - 150, 120, 120)
            g.DrawImage(qrCodeImage, qrCodeRect)

            ' Draw license key
            Dim licenseKeyFont As New Font("Consolas", 8)
            Dim licenseKeyRect As New Rectangle(certX + 20, certY + certHeight - 30, certWidth - 40, 20)
            g.DrawString("License Key: " & _license.LicenseKey, licenseKeyFont, detailsBrush, licenseKeyRect)

            ' Draw footer
            Dim footerFont As New Font("Segoe UI", 8)
            Dim footerRect As New Rectangle(certX + 20, certY + certHeight - 50, certWidth - 40, 20)
            g.DrawString("This license is subject to the terms and conditions of the software license agreement.", footerFont, detailsBrush, footerRect)
        End Sub

        ''' <summary>
        ''' Creates a platinum gradient brush
        ''' </summary>
        ''' <param name="rect">The rectangle to create the brush for</param>
        ''' <returns>A linear gradient brush with platinum colors</returns>
        Private Function CreatePlatinumGradientBrush(rect As Rectangle) As Drawing2D.LinearGradientBrush
            Dim brush As New Drawing2D.LinearGradientBrush(
                rect,
                Color.FromArgb(203, 213, 225), ' Light slate
                Color.FromArgb(71, 85, 105),   ' Dark slate
                Drawing2D.LinearGradientMode.ForwardDiagonal)

            Dim colorBlend As New Drawing2D.ColorBlend(4)
            colorBlend.Colors = New Color() {
                Color.FromArgb(203, 213, 225), ' Light slate
                Color.FromArgb(226, 232, 240), ' Lighter slate
                Color.FromArgb(148, 163, 184), ' Medium slate
                Color.FromArgb(71, 85, 105)    ' Dark slate
            }
            colorBlend.Positions = New Single() {0.0F, 0.3F, 0.7F, 1.0F}
            brush.InterpolationColors = colorBlend

            Return brush
        End Function

        ''' <summary>
        ''' Creates a gold gradient brush
        ''' </summary>
        ''' <param name="rect">The rectangle to create the brush for</param>
        ''' <returns>A linear gradient brush with gold colors</returns>
        Private Function CreateGoldGradientBrush(rect As Rectangle) As Drawing2D.LinearGradientBrush
            Dim brush As New Drawing2D.LinearGradientBrush(
                rect,
                Color.FromArgb(251, 191, 36), ' Amber
                Color.FromArgb(180, 83, 9),   ' Dark amber
                Drawing2D.LinearGradientMode.ForwardDiagonal)

            Dim colorBlend As New Drawing2D.ColorBlend(4)
            colorBlend.Colors = New Color() {
                Color.FromArgb(251, 191, 36), ' Amber
                Color.FromArgb(252, 211, 77), ' Light amber
                Color.FromArgb(217, 119, 6),  ' Medium amber
                Color.FromArgb(180, 83, 9)    ' Dark amber
            }
            colorBlend.Positions = New Single() {0.0F, 0.3F, 0.7F, 1.0F}
            brush.InterpolationColors = colorBlend

            Return brush
        End Function
    End Class
End Namespace