{"ast": null, "code": "import { useTranslation } from './useTranslation.js';\nexport const Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};", "map": {"version": 3, "names": ["useTranslation", "Translation", "ns", "children", "options", "t", "i18n", "ready", "lng", "language"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/New/React-Admin/node_modules/react-i18next/dist/es/Translation.js"], "sourcesContent": ["import { useTranslation } from './useTranslation.js';\nexport const Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,MAAMC,WAAW,GAAGA,CAAC;EAC1BC,EAAE;EACFC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ,MAAM,CAACC,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAACE,EAAE,EAAEE,OAAO,CAAC;EACpD,OAAOD,QAAQ,CAACE,CAAC,EAAE;IACjBC,IAAI;IACJE,GAAG,EAAEF,IAAI,CAACG;EACZ,CAAC,EAAEF,KAAK,CAAC;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}